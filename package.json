{"name": "board-web", "version": "0.0.0", "private": true, "type": "module", "scripts": {"dev": "vite --host 0.0.0.0", "build": "run-p type-check \"build-only {@}\" --", "preview": "vite preview", "build-only": "vite build", "type-check": "vue-tsc --build", "lint": "eslint . --fix", "format": "prettier --write src/", "uploadCos": "node ./scripts/uploadCos.js", "uploadServer": "node ./scripts/uploadServer.js"}, "dependencies": {"@element-plus/icons-vue": "^2.3.1", "@tailwindcss/vite": "^4.1.4", "@types/postcss-prefixer": "^2.1.3", "axios": "^1.9.0", "cos-nodejs-sdk-v5": "2.16.0-beta.3", "echarts": "^5.6.0", "element-plus": "^2.9.8", "lodash": "^4.17.21", "moment": "^2.30.1", "nats.ws": "^1.30.3", "pinia": "^3.0.1", "qrcode": "^1.5.4", "rimraf": "^6.0.1", "scp2": "^0.5.0", "tailwindcss": "^4.1.4", "vite-plugin-css-injected-by-js": "^3.5.2", "vite-plugin-qiankun": "^1.0.15", "vue": "^3.5.13", "vue-countup-v3": "^1.4.2", "vue-router": "^4.5.0", "vuedraggable": "^4.1.0"}, "devDependencies": {"@tsconfig/node22": "^22.0.1", "@types/node": "^22.14.0", "@vitejs/plugin-vue": "^5.2.3", "@vitejs/plugin-vue-jsx": "^4.1.2", "@vue/eslint-config-prettier": "^10.2.0", "@vue/eslint-config-typescript": "^14.5.0", "@vue/tsconfig": "^0.7.0", "eslint": "^9.22.0", "eslint-plugin-vue": "~10.0.0", "jiti": "^2.4.2", "npm-run-all2": "^7.0.2", "postcss-prefixer": "^3.0.0", "prettier": "3.5.3", "typescript": "~5.8.0", "vite": "^6.2.4", "vite-plugin-vue-devtools": "^7.7.2", "vue-tsc": "^2.2.8"}}