const scpClient = require('scp2');
const client = new scpClient.Client({
  host: '*************',
  username: 'ubuntu',
  password: '<PERSON>y@2025'
});

client.on('transfer', (buffer, uploaded, total) => {
  console.log(`进度: ${uploaded}/${total}`);
});

client.upload(
  'local_file.txt',
  '/www/wwwroot/www.3diy.word/',
  err => {
    if (err) console.error(err);
    else console.log('上传完成');
    client.close();
  }
);