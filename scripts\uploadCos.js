import COS from 'cos-nodejs-sdk-v5';
import path from 'node:path';
import { promises as fs } from 'node:fs';

// COS 配置
const cos = new COS({
  SecretId: process.env.COS_SECRET_ID, // 建议从环境变量获取
  SecretKey: process.env.COS_SECRET_KEY,
});

// 配置参数
const config = {
  region: 'ap-chengdu', // COS 地域
  bucket: 'diy3d-1253822988', // 格式: BucketName-APPID
  prefix: '/websites/diy3d/', // COS 存储路径前缀
  localDir: 'dist', // 本地构建目录
  exclude: ['*.map'], // 排除的文件类型
  timeout: 300000, // 上传超时时间
  concurrency: 5, // 并发上传数
};

// 常见文件后缀与 Content-Type 的映射表，可按需增删
const contentTypeMap = {
  '.html': 'text/html',
  '.htm': 'text/html',
  '.css': 'text/css',
  '.js': 'application/javascript',
  '.json': 'application/json',
  '.txt': 'text/plain',
  '.xml': 'application/xml',
  '.wasm': 'application/wasm',
  '.png': 'image/png',
  '.jpg': 'image/jpeg',
  '.jpeg': 'image/jpeg',
  '.gif': 'image/gif',
  '.svg': 'image/svg+xml',
  '.ico': 'image/x-icon',
  '.bmp': 'image/bmp',
  '.webp': 'image/webp',
  '.mp4': 'video/mp4',
  '.webm': 'video/webm',
  '.mp3': 'audio/mpeg',
  '.wav': 'audio/wav',
  '.ogg': 'audio/ogg',
  '.pdf': 'application/pdf',
  '.zip': 'application/zip',
  '.tar': 'application/x-tar',
  '.osgjs': 'application/json'
};

/**
 * 递归遍历指定目录，获取所有待上传的文件路径
 * 使用 Node.js 原生 fs\readdir 组合，而不依赖其它第三方库
 */
async function getFileList(dir = config.localDir) {
  const files = [];

  // 将类似 "*.map" 的排除规则转换为匹配逻辑
  const isExcluded = (fileName) => {
    return config.exclude.some((pattern) => {
      // 目前仅处理类似 "*.ext" 的简单通配
      if (pattern.startsWith('*.')) {
        return fileName.endsWith(pattern.slice(1)); // 去掉开头的 '*'
      }
      return false;
    });
  };

  async function walk(currentDir) {
    const dirents = await fs.readdir(currentDir, { withFileTypes: true });
    for (const dirent of dirents) {
      const fullPath = path.join(currentDir, dirent.name);

      // 跳过 node_modules（如有）
      if (dirent.isDirectory()) {
        if (dirent.name === 'node_modules') continue;
        await walk(fullPath);
      } else if (!isExcluded(dirent.name)) {
        files.push(fullPath);
      }
    }
  }

  try {
    await walk(dir);
    return files;
  } catch (error) {
    console.error('获取文件列表失败:', error);
    throw error;
  }
}

/**
 * 上传单个文件到 COS
 */
async function uploadFile(localPath) {
  try {
    // 计算 COS 上的路径
    const relativePath = path.relative(config.localDir, localPath);
    const cosKey = path.posix.join(config.prefix, relativePath).replace(/\\/g, '/');
    
    console.log(`Uploading: ${localPath} -> ${cosKey}`);
    
    // 根据文件扩展名决定是否设置 Content-Type
    const ext = path.extname(localPath).toLowerCase();
    const contentType = contentTypeMap[ext];

    const params = {
      Bucket: config.bucket,
      Region: config.region,
      Key: cosKey,
      Body: await fs.readFile(localPath),
      onProgress: (progressData) => {
        console.log(`${localPath}: ${Math.round(progressData.percent * 100)}%`);
      },
    };
    if (contentType) {
      params.ContentType = contentType;
    }

    const result = await cos.putObject(params);
    
    console.log(`Upload success: ${result.Location}`);
    return result;
  } catch (error) {
    console.error(`Upload failed ${localPath}:`, error);
    throw error;
  }
}

/**
 * 批量上传文件，控制并发数
 */
async function batchUploadFiles(files) {
  const queue = [];
  const results = [];
  let current = 0;
  
  for (const file of files) {
    // 如果当前并发数达到上限，等待其中一个完成
    if (current >= config.concurrency) {
      await Promise.race(queue);
    }
    
    const task = uploadFile(file)
      .then(res => {
        results.push(res);
        queue.splice(queue.indexOf(task), 1);
        current--;
        return res;
      })
      .catch(err => {
        queue.splice(queue.indexOf(task), 1);
        current--;
        throw err;
      });
    
    queue.push(task);
    current++;
  }
  
  // 等待所有剩余任务完成
  await Promise.all(queue);
  return results;
}

/**
 * 主函数
 */
async function main() {
  try {
    console.log('开始获取文件列表...');
    const files = await getFileList();
    console.log(`找到 ${files.length} 个文件待上传`);
    
    console.log('开始上传文件到 COS...');
    await batchUploadFiles(files);
    
    console.log('所有文件上传完成！');
  } catch (error) {
    console.error('上传过程中出错:', error);
    process.exit(1);
  }
}

// 执行主函数
main();