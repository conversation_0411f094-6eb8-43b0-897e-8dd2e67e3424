import path from "path";
import fs from "fs";
import sftp from "./uploadSftp.js";
import {
  rimraf
} from "rimraf";

var nativePath = `dist/temp`;

//创建临时目录
fs.mkdirSync(nativePath);


var indexHtml = fs.readFileSync("dist/index3.html");
fs.writeFileSync(nativePath + path.sep + "index3.html", indexHtml);

// var cssHtml = fs.readFileSync('dist/swiper-bundle.min.css');
// fs.writeFileSync(nativePath + path.sep + 'swiper-bundle.min.css', cssHtml);

// var editorHtml = fs.readFileSync('dist/editor.html');
// fs.writeFileSync(nativePath + path.sep + 'editor.html', editorHtml);

var serverPath = `/www/wwwroot/www.3diy.world/`;

var ftpUtils = new sftp({
  remotePath: serverPath,
  path: nativePath,
  username: "ubuntu",
  password: "Diy@2025",
  host: "*************",
  verbose: true,
});

ftpUtils.apply(function () {
  //删除maps文件夹
  rimraf.sync(nativePath);

  console.log(`ftp upload success!`);
});
