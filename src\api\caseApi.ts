import type { CaseApplication, CaseForm } from '@/stores/caseStore'
import { API_BASE_URL } from './config'

// API响应类型
interface ApiResponse<T> {
  result: T
  errorDesc?: string
  errorNo: number
}

// 请求配置
interface RequestConfig extends RequestInit {
  timeout?: number
}

// API请求封装
class CaseApi {
  private baseURL: string
  private defaultTimeout: number

  constructor(baseURL: string = API_BASE_URL + "/case", timeout: number = 10000) {
    this.baseURL = baseURL
    this.defaultTimeout = timeout
  }

  // 通用请求方法
  private async request<T>(
    endpoint: string, 
    config: RequestConfig = {}
  ): Promise<T> {
    const { timeout = this.defaultTimeout, ...fetchConfig } = config
    
    const controller = new AbortController()
    const timeoutId = setTimeout(() => controller.abort(), timeout)

    try {
      const response = await fetch(`${this.baseURL}${endpoint}`, {
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${this.getToken()}`,
          ...fetchConfig.headers,
        },
        signal: controller.signal,
        ...fetchConfig,
      })

      clearTimeout(timeoutId)

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }

      const result: ApiResponse<T> = await response.json()
      
      if (result.errorNo != 200) {
        throw new Error(result.errorDesc || '请求失败')
      }

      return result.result

    } catch (error) {
      clearTimeout(timeoutId)
      
      if (error instanceof Error) {
        if (error.name === 'AbortError') {
          throw new Error('请求超时')
        }
        throw error
      }
      
      throw new Error('网络请求失败')
    }
  }

  // 获取认证token
  private getToken(): string {
    return localStorage.getItem('token') || ''
  }

  // 获取案例列表
  async getCases(params?: {
    page?: number
    limit?: number
    status?: string
    isPublic?: boolean
    search?: string
  }): Promise<{
    cases: CaseApplication[]
    total: number
    page: number
    limit: number
  }> {
    const searchParams = new URLSearchParams()
    
    if (params) {
      Object.entries(params).forEach(([key, value]) => {
        if (value !== undefined && value !== null) {
          searchParams.append(key, String(value))
        }
      })
    }

    const queryString = searchParams.toString()
    const endpoint = queryString ? `?${queryString}` : ''
    
    return this.request<{
      cases: CaseApplication[]
      total: number
      page: number
      limit: number
    }>(endpoint)
  }

  // 获取单个案例详情
  async getCaseById(caseId: string): Promise<CaseApplication> {
    return this.request<CaseApplication>(`/${caseId}`)
  }

  // 创建案例
  async createCase(caseData: CaseForm): Promise<CaseApplication> {
    return this.request<CaseApplication>('', {
      method: 'POST',
      body: JSON.stringify(caseData),
    })
  }

  // 更新案例
  async updateCase(caseId: string, caseData: Partial<CaseForm>): Promise<CaseApplication> {
    return this.request<CaseApplication>(`/${caseId}`, {
      method: 'PUT',
      body: JSON.stringify(caseData),
    })
  }

  // 更新案例状态
  async updateCaseStatus(
    caseId: string, 
    status: CaseApplication['status'],
    reason?: string
  ): Promise<void> {
    return this.request<void>(`/${caseId}/status`, {
      method: 'POST',
      body: JSON.stringify({ status, reason }),
    })
  }

  // 删除案例
  async deleteCase(caseId: string): Promise<void> {
    return this.request<void>(`/${caseId}`, {
      method: 'DELETE',
    })
  }

  // 批量删除案例
  async deleteCases(caseIds: string[]): Promise<void> {
    return this.request<void>('/batch-delete', {
      method: 'POST',
      body: JSON.stringify({ caseIds }),
    })
  }

  // 上传附件
  async uploadAttachment(file: File): Promise<{ url: string; fileName: string }> {
    const formData = new FormData()
    formData.append('file', file)

    try {
      const response = await fetch(`${this.baseURL}/upload`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${this.getToken()}`,
        },
        body: formData,
      })

      if (!response.ok) {
        throw new Error('文件上传失败')
      }

      const result: ApiResponse<{ url: string; fileName: string }> = await response.json()
      
      if (result.errorNo != 200) {
        throw new Error(result.errorDesc || '文件上传失败')
      }

      return result.result
    } catch (error) {
      console.error('文件上传失败:', error)
      throw error
    }
  }

  // 删除附件
  async deleteAttachment(attachmentUrl: string): Promise<void> {
    return this.request<void>('/attachment', {
      method: 'DELETE',
      body: JSON.stringify({ url: attachmentUrl }),
    })
  }

  // 获取案例统计信息
  async getCaseStatistics(): Promise<{
    total: number
    pending: number
    approved: number
    rejected: number
    thisMonth: number
    lastMonth: number
  }> {
    return this.request<{
      total: number
      pending: number
      approved: number
      rejected: number
      thisMonth: number
      lastMonth: number
    }>('/statistics')
  }

  // 导出案例数据
  async exportCases(params?: {
    status?: string
    startDate?: string
    endDate?: string
    format?: 'excel' | 'csv'
  }): Promise<Blob> {
    const searchParams = new URLSearchParams()
    
    if (params) {
      Object.entries(params).forEach(([key, value]) => {
        if (value !== undefined && value !== null) {
          searchParams.append(key, String(value))
        }
      })
    }

    const queryString = searchParams.toString()
    const endpoint = `/export${queryString ? `?${queryString}` : ''}`

    try {
      const response = await fetch(`${this.baseURL}${endpoint}`, {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${this.getToken()}`,
        },
      })

      if (!response.ok) {
        throw new Error('导出失败')
      }

      return response.blob()
    } catch (error) {
      console.error('导出失败:', error)
      throw error
    }
  }

  // 批量更新案例状态
  async batchUpdateStatus(
    caseIds: string[], 
    status: CaseApplication['status'],
    reason?: string
  ): Promise<void> {
    return this.request<void>('/batch-status', {
      method: 'POST',
      body: JSON.stringify({ caseIds, status, reason }),
    })
  }

  // 获取案例审核历史
  async getCaseHistory(caseId: string): Promise<Array<{
    id: string
    action: string
    status: string
    reason?: string
    operator: string
    createdAt: string
  }>> {
    return this.request<Array<{
      id: string
      action: string
      status: string
      reason?: string
      operator: string
      createdAt: string
    }>>(`/${caseId}/history`)
  }

  // 搜索案例
  async searchCases(query: string, filters?: {
    status?: string
    isPublic?: boolean
    dateRange?: [string, string]
  }): Promise<CaseApplication[]> {
    const searchParams = new URLSearchParams()
    searchParams.append('q', query)
    
    if (filters) {
      Object.entries(filters).forEach(([key, value]) => {
        if (value !== undefined && value !== null) {
          if (Array.isArray(value)) {
            searchParams.append(`${key}[0]`, value[0])
            searchParams.append(`${key}[1]`, value[1])
          } else {
            searchParams.append(key, String(value))
          }
        }
      })
    }

    return this.request<CaseApplication[]>(`/search?${searchParams.toString()}`)
  }
}

// 创建API实例
export const caseApi = new CaseApi()

// 导出API类型
export type { ApiResponse, RequestConfig }
export default CaseApi 