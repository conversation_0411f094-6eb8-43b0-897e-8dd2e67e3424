import { API_BASE_URL } from './config'

// 上传凭证响应接口
export interface UploadToken {
  access_key_id: string
  policy: string
  signature: string
  dir: string
  host: string
  expire: number
  callback?: any
}

// Celery任务结果接口
export interface CeleryTaskResult {
  file_name: string
  file_size_mb: number
  statistics: {
    transcription_time: number
  }
  speaker_segments_count: number
  speakers: string[]
  result_url: string
  completed_at: string
  storage_type: 'oss' | 'local'
  audio_duration_seconds: number
}

// 转录片段接口
export interface SpeakerSegment {
  start_time: number
  end_time: number
  speaker: string
  text: string
}

// 转录结果接口
export interface TranscriptionResult {
  file_path: string
  file_name: string
  file_size_mb: number
  speaker_segments: SpeakerSegment[]
}

// 会议详情响应接口 - 包含完整字段
export interface MeetingDetailResponse {
  id: number
  name: string
  users?: string | null
  meetType?: string | null
  desc?: string | null
  user_id: number
  audioOriginUrls?: string | null
  audioOriginSize?: number | null
  audioDuration?: number | null
  audioUrl?: string | null
  audioSize?: number | null
  audioState?: string | null
  celeryTaskId?: string | null
  celeryTaskStatus?: string | null
  celeryTaskResult?: string | null
  created_at?: string | null
  updated_at?: string | null
}

// 会议响应接口 - 匹配后端MeetingResponse（列表用）
export interface MeetingResponse {
  id: number
  name: string
  users?: string | null
  meetType?: string | null
  desc?: string | null
  created_at?: string | null
  updated_at?: string | null
  audioState?: string | null
  audioDuration?: number | null
  audioOriginSize?: number | null
}

// 分页会议响应接口 - 匹配后端PaginatedMeetingResponse
export interface PaginatedMeetingResponse {
  meetings: MeetingResponse[]
  total: number
  page: number
  page_size: number
  total_pages: number
}

// HTTP结果响应接口 - 匹配后端HttpResult
export interface HttpResult<T> {
  data: T
  message?: string
  code: number
}

// 会议创建请求接口
export interface CreateMeetingRequest {
  name: string
  meetType?: string
  users?: string
  desc?: string
  audioOriginUrls: string
  audioOriginSize?: number
  audioDuration?: number
  audioUrl?: string
  audioSize?: number
  audioState: string
}

// 会议列表查询参数
export interface MeetingListParams {
  page?: number
  page_size?: number
}

// API响应类型
interface ApiResponse<T> {
  data: T
  message?: string
  code: number
}

// 会议API类
class MeetingApi {
  private baseURL: string
  private defaultTimeout: number

  constructor(baseURL: string = API_BASE_URL, timeout: number = 30000) {
    this.baseURL = baseURL
    this.defaultTimeout = timeout
  }

  // 通用请求方法
  private async request<T>(
    endpoint: string,
    config: RequestInit = {}
  ): Promise<T> {
    const controller = new AbortController()
    const timeoutId = setTimeout(() => controller.abort(), this.defaultTimeout)

    try {
      const response = await fetch(`${this.baseURL}${endpoint}`, {
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `${this.getToken()}`,
          ...config.headers,
        },
        signal: controller.signal,
        ...config,
      })

      clearTimeout(timeoutId)

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }

      const result: ApiResponse<T> = await response.json()
      
      if (result.code != 200) {
        throw new Error(result.message || '请求失败')
      }

      return result.data

    } catch (error) {
      clearTimeout(timeoutId)
      
      if (error instanceof Error) {
        if (error.name === 'AbortError') {
          throw new Error('请求超时')
        }
        throw error
      }
      
      throw new Error('网络请求失败')
    }
  }

  // 获取认证token
  private getToken(): string {
    return localStorage.getItem('token') || ''
  }

  // 获取OSS上传凭证
  async getUploadToken(): Promise<UploadToken> {
    try {
      const response = await fetch(`${this.baseURL}/api/oss/upload-token`, {
        method: 'POST',
        body: JSON.stringify({}),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `${this.getToken()}`
        }
      })

      if (!response.ok) {
        throw new Error('获取上传凭证失败')
      }

      const data = await response.json()
      return data
    } catch (error) {
      console.error('获取上传凭证失败:', error)
      throw new Error('获取上传凭证失败')
    }
  }

  // 上传文件到OSS
  async uploadFileToOSS(file: File, token: UploadToken): Promise<string> {
    const formData = new FormData()
    const filename = `${Date.now()}_${file.name}`
    const key = `${token.dir}${filename}`

    formData.append('key', key)
    formData.append('policy', token.policy)
    formData.append('OSSAccessKeyId', token.access_key_id)
    formData.append('signature', token.signature)
    formData.append('x-oss-object-acl', 'public-read')
    formData.append('file', file)

    try {
      const response = await fetch(token.host, {
        method: 'POST',
        body: formData
      })

      if (!response.ok) {
        throw new Error('文件上传失败')
      }

      return `${token.host}/${key}`
    } catch (error) {
      console.error('文件上传失败:', error)
      throw new Error('文件上传失败')
    }
  }

  // 创建会议
  async createMeeting(meetingData: CreateMeetingRequest): Promise<any> {
    return this.request('/api/meeting/create', {
      method: 'POST',
      body: JSON.stringify(meetingData),
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `${this.getToken()}`
      }
    })
  }

  // 获取会议列表 - 更新为匹配后端接口
  async getMeetingList(params?: MeetingListParams): Promise<PaginatedMeetingResponse> {
    const searchParams = new URLSearchParams()
    
    if (params?.page) {
      searchParams.append('page', String(params.page))
    }
    if (params?.page_size) {
      searchParams.append('page_size', String(params.page_size))
    }

    const queryString = searchParams.toString()
    const endpoint = `/api/meeting/list${queryString ? `?${queryString}` : ''}`
    
    return this.request<PaginatedMeetingResponse>(endpoint)
  }

  // 保留原有的getMeetings方法以保持兼容性
  async getMeetings(params?: {
    page?: number
    limit?: number
    status?: string
    search?: string
  }): Promise<{
    meetings: any[]
    total: number
    page: number
    limit: number
  }> {
    const searchParams = new URLSearchParams()
    
    if (params) {
      Object.entries(params).forEach(([key, value]) => {
        if (value !== undefined && value !== null) {
          searchParams.append(key, String(value))
        }
      })
    }

    const queryString = searchParams.toString()
    const endpoint = `/api/meeting${queryString ? `?${queryString}` : ''}`
    
    return this.request(endpoint)
  }

  // 获取单个会议详情
  async getMeetingById(meetingId: string): Promise<any> {
    return this.request(`/api/meeting/${meetingId}`)
  }

  // 获取会议详情 - 新增的详情接口
  async getMeetingDetail(meetingId: number): Promise<MeetingDetailResponse> {
    return this.request<MeetingDetailResponse>(`/api/meeting/detail/${meetingId}`)
  }

  // 获取转录结果
  async getTranscriptionResult(url: string): Promise<TranscriptionResult> {
    try {
      const response = await fetch(url, {
        method: 'GET',
        headers: {
          'Accept': 'application/json',
        }
      })

      if (!response.ok) {
        throw new Error(`获取转录结果失败: ${response.status}`)
      }

      return await response.json()
    } catch (error) {
      console.error('获取转录结果失败:', error)
      throw new Error('获取转录结果失败')
    }
  }

  // 解析celeryTaskResult字符串
  parseCeleryTaskResult(celeryTaskResult?: string | null): CeleryTaskResult | null {
    if (!celeryTaskResult) return null
    
    try {
      return JSON.parse(celeryTaskResult).result as CeleryTaskResult
    } catch (error) {
      console.error('解析celeryTaskResult失败:', error)
      return null
    }
  }

  // 更新会议
  async updateMeeting(meetingId: string, meetingData: Partial<CreateMeetingRequest>): Promise<any> {
    return this.request(`/api/meeting/${meetingId}`, {
      method: 'PUT',
      body: JSON.stringify(meetingData),
    })
  }

  // 删除会议
  async deleteMeeting(meetingId: string): Promise<void> {
    return this.request(`/api/meeting/${meetingId}`, {
      method: 'DELETE',
    })
  }

  // 删除会议 - 匹配后端接口
  async deleteMeetingById(meetingId: number): Promise<string> {
    return this.request<string>(`/api/meeting/delete/${meetingId}`, {
      method: 'POST',
    })
  }

  // 重新转换任务 - 匹配后端接口
  async retryTask(meetingId: number): Promise<string> {
    return this.request<string>(`/api/meeting/retry-task/${meetingId}`, {
      method: 'POST',
      body: JSON.stringify({
        force: false
      })
    })
  }

  // 获取会议音频处理状态
  async getMeetingAudioStatus(meetingId: string): Promise<{
    status: string
    progress: number
    result?: any
  }> {
    return this.request(`/api/meeting/${meetingId}/audio-status`)
  }

  // 开始音频处理
  async startAudioProcessing(meetingId: string): Promise<void> {
    return this.request(`/api/meeting/${meetingId}/start-processing`, {
      method: 'POST',
    })
  }
}

// 导出API实例
export const meetingApi = new MeetingApi()
export default meetingApi 