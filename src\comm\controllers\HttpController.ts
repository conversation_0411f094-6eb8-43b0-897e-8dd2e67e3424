import axios from "axios";
import type { AxiosRequestConfig } from "axios";

export function createRequestHandlers(
  handlers: {
    request?: (req: AxiosRequestConfig) => void;
    response?: (res: any) => any;
  }[]
) {
  return {
    request: handlers.map(({ request }) => {
      return function () {
        //@ts-ignore
        const req = this;
        //@ts-ignore
        request?.(req);
        return JSON.stringify(req.data);
      };
    }),
    response: (res: any) => {
      handlers.forEach(({ response }) => {
        res = response?.(res) || res;
      });
      return res;
    },
  };
}

export type RequestConfig =  AxiosRequestConfig & {
  originBody?: boolean;
  prefix?: string;
  handlers?: ReturnType<typeof createRequestHandlers>;
};

const baseReqConfig = {
  timeout: 90000,
  headers: { "Content-Type": "application/json; charset=utf-8" },
};

export function createRequestController(defReqConfig: () => RequestConfig) {
  return async function (url: string, config: RequestConfig): Promise<{ errorNo: number, errorDesc?: string, result?: any }> {
    const { originBody, handlers, prefix, ...thisConfig } = Object.assign(
      {},
      baseReqConfig,
      defReqConfig(),
      config,
      { url }
    );

    if (prefix) {
      thisConfig.url = prefix + thisConfig.url;
    }

    if (handlers?.request) {
      thisConfig.transformRequest = handlers.request;
    }

    let response: {errorNo:number, errorDesc?:string, result?:any};
    
    try {
      response = (await axios(thisConfig)).data;
      if (originBody) {
        response = {
          errorNo: 200,
          errorDesc: "",
          result: response,
        };
      }
    } catch (error: any) {
      response = {
        errorNo: 400,
        errorDesc: error.toString(),
      };
    }

    if (handlers?.response) {
      return handlers?.response(response);
    }

    const result = response;
    return {
      errorNo: result.errorNo,
      errorDesc: result.errorDesc,
      result: result.result
    };
  };
}

