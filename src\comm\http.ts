import { API_BASE_URL } from "@/api/config"
import {createRequestController} from "./controllers/HttpController"

const request = createRequestController(()=>({"prefix": API_BASE_URL}))

function submitIdea(ideaData: {
    name: string;
    imageUrl: string;
    description: string;
    contact: string;
    phone: string;
    isPublic: boolean;
    attachments?: string[];
}) {
    return request("/idea", {
        method: "POST",
        data: ideaData
    })
}

function cosPolicy(ext:string) {
    return request("/cos/policy", {
        method: "GET",
        params: {ext}
    })
}

export {submitIdea, cosPolicy}
