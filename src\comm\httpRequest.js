import COSAuth from './cos-auth.js';
import dayjs from 'dayjs';
import relativeTime  from 'dayjs/plugin/relativeTime';
import localizedFormat from 'dayjs/plugin/localizedFormat';
import zhCn from 'dayjs/locale/zh-cn';
import utils from "@/utils/util.js"

dayjs.extend(localizedFormat);
dayjs.locale('zh-cn');
dayjs.extend(relativeTime);

function isSubset(arr1, arr2) {
	return arr1.every(item => arr2.includes(item));
}

const tui = {
	//接口地址
	interfaceUrl: function () {
		// return 'http://47.108.184.14:8908/coffee/';
		// return 'http://192.168.110.180:8908/coffee/';
		return 'https://www.scstpp.online/api/';
		// return 'http://172.17.144.132:8908/coffee/';
		//return 'https://test.thorui.cn'
		//return 'https://uat.thorui.cn'
		// return 'https://prod.thorui.cn'
	},
	toast: function (text, duration, success) {
		uni.showToast({
			title: text || "出错啦~",
			icon: success ? 'success' : 'none',
			duration: duration || 2000
		})
	},
	modal: function (title, content, showCancel, callback, confirmColor, confirmText) {
		uni.showModal({
			title: title || '提示',
			content: content,
			showCancel: showCancel,
			cancelColor: "#555",
			confirmColor: confirmColor || "#5677fc",
			confirmText: confirmText || "确定",
			success(res) {
				if (res.confirm) {
					callback && callback(true)
				} else {
					callback && callback(false)
				}
			}
		})
	},
	isAndroid: function () {
		const res = uni.getSystemInfoSync();
		return res.platform.toLocaleLowerCase() == "android"
	},
	isPhoneX: function () {
		const res = uni.getSystemInfoSync();
		let iphonex = false;
		let models = ['iphonex', 'iphonexr', 'iphonexsmax', 'iphone11', 'iphone11pro', 'iphone11promax']
		const model = res.model.replace(/\s/g, "").toLowerCase()
		if (models.includes(model)) {
			iphonex = true;
		}
		return iphonex;
	},
	constNum: function () {
		let time = 0;
		// #ifdef APP-PLUS
		time = this.isAndroid() ? 300 : 0;
		// #endif
		return time
	},
	delayed: null,
	showLoading: function (title, mask = true) {
		uni.showLoading({
			mask: mask,
			title: title || '请稍候...'
		})
	},
	/**
	 * 请求数据处理
	 * @param string url 请求地址
	 * @param string method 请求方式
	 *  GET or POST
	 * @param {*} postData 请求参数
	 * @param bool isDelay 是否延迟显示loading
	 * @param bool isForm 数据格式
	 *  true: 'application/x-www-form-urlencoded'
	 *  false:'application/json'
	 * @param bool hideLoading 是否隐藏loading
	 *  true: 隐藏
	 *  false:显示
	 */
	request: async function (url, method, postData, isDelay, isForm, hideLoading) {
		//接口请求
		let loadding = false;
		tui.delayed && uni.hideLoading();
		clearTimeout(tui.delayed);
		tui.delayed = null;
		if (!hideLoading) {
			if (isDelay) {
				tui.delayed = setTimeout(() => {
					loadding = true
					tui.showLoading()
				}, 1000)
			} else {
				loadding = true
				tui.showLoading()
			}
		}

		return new Promise((resolve, reject) => {

			if (url.substring(0, 4) != "http") {
				url = tui.interfaceUrl() + url;
			}

			uni.request({
				url: url,
				data: postData,
				header: {
					'content-type': isForm ? 'application/x-www-form-urlencoded' : 'application/json',
					'Authorization': tui.getToken()
				},
				method: method, //'GET','POST'
				dataType: 'json',
				success: (res) => {
					clearTimeout(tui.delayed)
					tui.delayed = null;
					if (loadding && !hideLoading) {
						uni.hideLoading()
					}
					resolve(res.data)
				},
				fail: (res) => {
					clearTimeout(tui.delayed)
					tui.delayed = null;
					tui.toast("网络不给力，请稍后再试~")
					reject(res)
				}
			})
		})
	},
	/**
	 * 上传文件
	 * @param string url 请求地址
	 * @param string src 文件路径
	 */
	uploadFile: function (url, src) {
		tui.showLoading()
		return new Promise((resolve, reject) => {
			const uploadTask = uni.uploadFile({
				url: tui.interfaceUrl() + url,
				filePath: src,
				name: 'imageFile',
				header: {
					'Authorization': tui.getToken()
				},
				formData: {
					// sizeArrayText:""
				},
				success: function (res) {
					uni.hideLoading()
					let d = JSON.parse(res.data.replace(/\ufeff/g, "") || "{}")
					if (d.code % 100 == 0) {
						//返回图片地址
						let fileObj = d.data;
						resolve(fileObj)
					} else {
						that.toast(res.msg);
					}
				},
				fail: function (res) {
					reject(res)
					that.toast(res.msg);
				}
			})
		})
	},
	tuiJsonp: function (url, callback, callbackname) {
		// #ifdef H5
		window[callbackname] = callback;
		let tuiScript = document.createElement("script");
		tuiScript.src = url;
		tuiScript.type = "text/javascript";
		document.head.appendChild(tuiScript);
		document.head.removeChild(tuiScript);
		// #endif
	},
	//设置用户信息
	setUserInfo: function (mobile, token) {
		uni.setStorageSync("thorui_token", token)
		uni.setStorageSync("thorui_mobile", mobile)
	},
	//获取token
	getToken() {
		return uni.getStorageSync("thorui_token")
	},
	//判断是否登录
	isLogin: function () {
		return uni.getStorageSync("thorui_mobile") ? true : false
	},
	loginout: function () {
		// 移除数据
		uni.removeStorageSync("thorui_token");
	},
	//跳转页面，校验登录状态
	href(url, isVerify) {
		if (isVerify && !tui.isLogin()) {
			uni.navigateTo({
				url: '/pages/common/login/login'
			})
		} else {
			uni.navigateTo({
				url: url
			});
		}
	},
	skuTotalPrice(sku) { //总价=sku.price + spec.price
		let p = sku.price;
		const specs = sku.specs || [];
		specs.forEach(item => {
			if (item.index != -1) {
				const p1 = item.options[item.index].price;
				if (p1 == undefined) return;
				if (p1 != 0) {
					p += p1;
				}
			}
		})
		return p;
	},
	skuCurrSpecsNames(sku) {
		if (!sku) return "";
		
		const out = []
		const specs = sku.specs || [];
		specs.forEach(item => {
			if (item.index != undefined && item.index != -1) {
				out.push(item.options[item.index].name)
			}
		})
		if (out.length < 1) return ""
		return out.join(",")
	},
	hasFenQi(sku) {
		if (!sku) return false;
		const specs = sku.specs || [];
		const n = specs.length;
		for (let i=0; i<n; i++) {
			const item = specs[i];
			if (item.name != "分期") continue;
			
			
			if (item.index != undefined && item.index != -1) {
				const opt = item.options[item.index]
				if (opt.totalMonth > 0) return true;
			}
		}
		
		return  false;
	},
	
	skuCurrThumb(sku) {
		const thumbs = sku.thumbs || [];
		const specsSelected = []
		const specs = sku.specs || [];
		specs.forEach(item => {
			if (item.index != undefined && item.index != -1) {
				specsSelected.push(item.options[item.index].id)
			}
		})

		if (specsSelected.length < 1) {
			return sku.cover;
		}
		const preOpts = [];
		for (let i = 0; i < thumbs.length; i++) {
			const options = thumbs[i].options || []
			const size = options.length;
			if (isSubset(options, specsSelected) && size > 0) {
				preOpts.push(thumbs[i])

				if (options.length == specsSelected.length) return thumbs[i].url
			}
		}
		if (preOpts.length > 0) {
			if (preOpts.length == 1) return preOpts[0].url;
			preOpts.sort((a, b) => b.options.length - a.options.length);
			return preOpts[0].url;
		}
		return sku.cover;
	},

	skuFirstPayPrice(sku) {
		var firtpay = -1;
		const specs = sku.specs || [];
		specs.forEach(item => {
			if (item.index != -1) {
				const p1 = item.options[item.index].firstPayPrice;
				if (p1 == undefined) return;
				if (p1 != 0) {
					firtpay += p1;
				}
			}
		})
		return firtpay;
	},
	skuInstancePlanDesc(sku) {
		var out = "";
		const specs = sku.specs || [];
		for (var i = 0; i < specs.length; i++) {
			var item = specs[i];

			if (item.name != "分期" || item.index < 0) continue;

			var option = item.options[item.index]
			if (option.name == "不分期")  return "";
			
			var p = option.pricePerMonth || 0;
			
			var perM = (p / 100.0).toFixed(2);
			var p1 = (option.firstPayPrice / 100.0).toFixed(2)
			out = `首付: ¥${p1}; 每期:¥${perM}`
			break
		}
		return out;
	},
	skuFenGiFirtPay(sku) {
		let out = 0;
		const specs = sku.specs || [];
		for (var i = 0; i < specs.length; i++) {
			var item = specs[i];

			if (item.name != "分期" || item.index < 0) continue;

			var option = item.options[item.index]
			out = option.firstPayPrice
			break
		}
		return out;
	},
	skuTotalFirtPay(sku) {
		let out = 0;
		let findFenQi = false
		const specs = sku.specs || [];
		for (let i = 0; i < specs.length; i++) {
			const item = specs[i];
			if (item.index == -1) continue
			const option = item.options[item.index]
			if (item.name != "分期") {
				continue
			}
			if (option.totalMonth != 0) {
				findFenQi = true
				break
			}
		}

		if (findFenQi) {
			for (let i = 0; i < specs.length; i++) {
				const item = specs[i];
				if (item.index == -1) continue
				const option = item.options[item.index]
				if (item.name != "分期") {
					out += (option.price || 0)
					continue
				}
				out += (option.firstPayPrice || 0)
			}
		} else {
			for (let i = 0; i < specs.length; i++) {
				const item = specs[i];
				if (item.index == -1) continue
				const option = item.options[item.index]
				out += (option.price || 0)
			}
			out += sku.price
		}
		return out;
	},
	showModal(title, content) {
		return new Promise((resolve, reject)=>{
			uni.showModal({
			  title: title,
			  content: content,
			  success: (res)=>{
				if (res.confirm) {
					resolve(true)
					return;
				} 
				resolve(false)
			  }
			});
		})
	},
	
	uploadCos: (tempFilePath)=>{
		const filename = tempFilePath;
		const fileExt = filename.substring(filename.lastIndexOf('.') + 1);
		
		return new Promise((resolve, reject)=>{
			try {
				
				tui.request("cos/policy?ext=" + fileExt, "GET").then((ret)=>{
					if (!ret || ret.errorNo != 200) {
						uni.showToast({
							title: ret.errorDesc,
							icon: 'none'
						})
						uni.hideLoading();
						resolve("")
						return;
					}
					const credentials = ret.result;
										
					uni.uploadFile({
						url: credentials.cosHost,
						filePath: tempFilePath,
						name: "file",
						formData: {
							"key": credentials.cosKey,
							"q-sign-algorithm": credentials.qSignAlgorithm,
							"q-ak": credentials.qAk,
							"q-key-time": credentials.qKeyTime,
							"q-signature": credentials.qSignature,
							"policy": credentials.policy
						},
						success: (uploadFileRes) => {
							uni.hideLoading();
							resolve(credentials.cosHost + "/" + credentials.cosKey)
						},
						fail: (e) => {
							console.log("upload failed", e);
							uni.hideLoading();
							resolve("");
						}
					});
				});		
			} catch (error) {
				// this.uploading = false;
				console.error('获取 STS 临时密钥失败:', error);
				uni.showToast({
					title: '上传失败',
					icon: 'none'
				});
				uni.hideLoading();
				resolve("");
			}
		});
	},
	
	uploadCosFile2: async function(maxM=10, confirm=true, ext = "") {
		
		const filedata = await utils.chooseFile()
		if (ext) {
			if( !filedata.tempFile.name.toLowerCase().endsWith(ext) ) {
				tui.toast(`只支持${ext}格式文件!`)
				return;
			} 
		}
		if (confirm) {
			const ok = await tui.showModal("提示", "确认上传文件"+filedata.tempFile.name);
			if (!ok) {
				return ""
			}
		}
		
		uni.showLoading({
			title: "上传中"
		})
		
		const file = filedata.tempFile;
		const filename = file.name || file.path;
		const size = file.size
		if (size > (1024 * 1024 * maxM)) {
			uni.showToast({
				title: `文件不能超过${maxM}M！`,
				icon: 'none'
			})
			resolve("");
			return;
		}
		return await this.uploadCos(filedata.tempFilePath)
	},
		
	uploadCosFile(sourceType = [], maxM=7) {

		return new Promise((resolve, reject) => {


			const params = {
				count: 1,
				success: async (res) => {
					if (res.tempFiles.length < 1) return;


					uni.showLoading({
						title: "上传中"
					})

					const tempFilePath = res.tempFilePaths[0];
					console.log('选择的文件:', tempFilePath);
					const filename = res.tempFiles[0].name || res.tempFiles[0].path;
					const size = res.tempFiles[0].size
					if (size > (1024 * 1024 * maxM)) {
						uni.showToast({
							title: `文件不能超过${maxM}M！`,
							icon: 'none'
						})
						resolve("");
						return;
					}
					console.log("xxxxxxxxx2222")
					// 从路径中提取文件名
					const fileExt = filename.substring(filename.lastIndexOf('.') + 1);

					try {
						// 获取 STS 临时密钥
						const ret = await tui.request("cos/policy?ext=" + fileExt, "GET");
						if (!ret || ret.errorNo != 200) {
							uni.showToast({
								title: ret.errorDesc,
								icon: 'none'
							})
							uni.hideLoading();
							return;
						}

						const credentials = ret.result;

						console.log("xxxxxxxxx23332")
						uni.uploadFile({
							url: credentials.cosHost,
							filePath: tempFilePath,
							name: "file",
							formData: {
								"key": credentials.cosKey,
								"q-sign-algorithm": credentials.qSignAlgorithm,
								"q-ak": credentials.qAk,
								"q-key-time": credentials.qKeyTime,
								"q-signature": credentials.qSignature,
								"policy": credentials.policy
							},
							success: (uploadFileRes) => {
								uni.hideLoading();
								resolve(credentials.cosHost + "/" + credentials.cosKey)
							},
							fail: (e) => {
								console.log("upload failed", e);
								uni.hideLoading();
								resolve("");
							}
						});

					} catch (error) {
						// this.uploading = false;
						console.error('获取 STS 临时密钥失败:', error);
						uni.showToast({
							title: '上传失败',
							icon: 'none'
						});
						uni.hideLoading();
						resolve("");
					}
				},
			}


			if (sourceType.length > 0) {
				params.sourceType = sourceType;
			}
			uni.chooseImage(params);
		})
	}
	// #ifdef MP-WEIXIN
	,
	uploadBase64ImageFile(base64image) {
		console.log("---", base64image);


		async function upload(resolve, fileExt, tempFilePath) {
			try {
				// 获取 STS 临时密钥
				const ret = await tui.request("cos/policy?ext=" + fileExt, "GET");
				if (!ret || ret.errorNo != 200) {
					uni.showToast({
						title: ret.errorDesc,
						icon: 'none'
					})
					uni.hideLoading();
					return;
				}

				const credentials = ret.result;

				console.log("xxxxxxxxx23332")
				uni.uploadFile({
					url: credentials.cosHost,
					filePath: tempFilePath,
					name: "file",
					formData: {
						"key": credentials.cosKey,
						"q-sign-algorithm": credentials.qSignAlgorithm,
						"q-ak": credentials.qAk,
						"q-key-time": credentials.qKeyTime,
						"q-signature": credentials.qSignature,
						"policy": credentials.policy
					},
					success: (uploadFileRes) => {
						uni.hideLoading();
						resolve(credentials.cosHost + "/" + credentials.cosKey)
					},
					fail: (e) => {
						console.log("upload failed", e);
						uni.hideLoading();
						resolve("");
					}
				});

			} catch (error) {
				// this.uploading = false;
				console.error('获取 STS 临时密钥失败:', error);
				uni.showToast({
					title: '上传失败',
					icon: 'none'
				});
				uni.hideLoading();
				resolve("");
			}
		}

		return new Promise((resolve, reject) => {


			// 提取 base64 数据和 MIME 类型
			const matches = base64image.match(/^data:(image\/\w+);base64,(.+)$/);
			if (!matches || matches.length !== 3) {

				if (base64image.substring(0, 7) == "http://" || base64image.substring(0, 9) == "wxfile://") {
					const i = base64image.lastIndexOf(".") + 1
					upload(resolve, base64image.substring(i), base64image);
					return;
				}

				console.log('base64image is not valid', base64image.substring(0, 10))
				resolve("");
				return;
			}

			const mimeType = matches[1]; // 例如 "image/png"
			const base64String = matches[2]; // 纯 base64 数据
			const fileExt = mimeType.split("/")[1]; // 例如 "png"

			const fileName = `${date.now()}.${fileExt}`; // 生成文件名

			// 将 base64 数据转换为 ArrayBuffer
			const arrayBuffer = wx.base64ToArrayBuffer(base64String);

			// 获取临时文件路径
			const tempFilePath = `${wx.env.USER_DATA_PATH}/${fileName}`;

			// 将 ArrayBuffer 写入临时文件
			const fileManager = wx.getFileSystemManager();
			fileManager.writeFile({
				filePath: tempFilePath,
				data: arrayBuffer,
				encoding: 'binary', // 注意：这里使用 binary 编码
				success: () => {
					upload(resolve, fileExt, tempFilePath);
				},
				fail: (err) => {
					console.log("Write file failed:", err);
					resolve("");
				}
			});
		})
	}
	// #endif
	,
	orderState(status) {
		if (status < 0 || status > 5) return "未知状态";

		return ["等待您付款", "待发货", "待收货", "订单已完成", "交易关闭"][status - 1]
	},
	
	formNow(isoDateString) {
		// 假设你有一个时间点
		const pastTime = dayjs(isoDateString);
		// 使用 fromNow 方法
		return pastTime.fromNow();	
	},
	formatTime(isoDateString) {
		return dayjs(isoDateString).format('YYYY-MM-DD HH:mm:ss');
	},
	
	formatDate(isoDateString) {
		return dayjs(isoDateString).format('YYYY-MM-DD');
	},
	formatInstallState(state) {
		const names = ["申请待审批", "合同待上传", "合同待审核", "待付款", "进行中", "已完成", "申请不通过"]
		return names[state-1];
	},
	formatFix2(num) {
		if ((num % 100) ==0) {
			return "" + num / 100;
		}
		if ((num%10) == 0) {
			return (num/100).toFixed(1)
		}
		return (num/100).toFixed(2)
	},
	littleNum(v) {
	   if (v % 100  == 0) return ""
	   return `.${v%100}`
	},
	urlName(url) {
		return url.substring(url.lastIndexOf("/")+1)
	}
}

export default tui