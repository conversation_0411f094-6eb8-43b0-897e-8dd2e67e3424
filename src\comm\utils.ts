import { ElMessage } from 'element-plus';
import {cosPolicy} from "./http"

// 格式化数值
 const formatValue = (value: number, type: string) => {
    switch (type) {
        case 'money':
            return `¥${value.toLocaleString('zh-CN', { maximumFractionDigits: 2 })}`
        case 'percent':
            return `${(value * 100).toFixed(2)}%`
        case 'duration':
            return `${Math.floor(value / 60)}分${value % 60}秒`
        case 'number':
            return value.toLocaleString('zh-CN')
        default:
            return value
    }
  }

  // 格式化趋势
  const formatTrend = (trend: number) => {
    return trend >= 0 ? `+${(trend * 100).toFixed(1)}%` : `${(trend * 100).toFixed(1)}%`
  }

  function chooseFile(acceptTypes = 'image/jpeg,image/png,image/gif,.jpg,.jpeg,.png,.gif') {
        return new Promise((resolve, reject) => {
            const input = document.createElement('input');
            input.type = 'file';
            
            // 默认接收常见图片格式
            input.accept = acceptTypes;
            
            input.onchange = (e:any) => {
                const file = e.target.files[0];
                resolve(file);
            };
            input.click();
        });
    }

    const uploadCosFile = async(isImage=true, maxM=10)=>{
        const file = await chooseFile(isImage? 'image/jpeg,image/png,image/gif,.jpg,.jpeg,.png,.gif' : '*') as any
        const size = file.size
        if (size > (1024 * 1024 * maxM)) {
            ElMessage.error(`文件不能超过${maxM}M！`)
            return;
        }
        const fileExt = file.name.substring(file.name.lastIndexOf('.') + 1);

        const ret = await cosPolicy(fileExt);

        if (!ret || ret.errorNo != 200) {
            ElMessage(ret.errorDesc || "上传失败")
            return;
        }
        const credentials = ret.result;
        
        const formData = new FormData();
            formData.append("key", credentials.cosKey);
            formData.append("q-sign-algorithm", credentials.qSignAlgorithm);
            formData.append("q-ak", credentials.qAk);
            formData.append("q-key-time", credentials.qKeyTime);
            formData.append("q-signature", credentials.qSignature);
            formData.append("policy", credentials.policy);
            formData.append("file", file);

        const  response =  await fetch(credentials.cosHost, {
                method: 'POST',
                body: formData
            })
            
        if (response.ok) {
            return credentials.cosHost + "/" + credentials.cosKey;
        } else {
            console.log("upload failed", response);
            return "";
        }
    }
	
  export {formatValue, formatTrend,  uploadCosFile}