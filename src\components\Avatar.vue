<template>
    <div class="w-12 h-12 rounded-full overflow-hidden bg-gray-100 flex items-center justify-center">
        <img :src="avatorUrl" class="w-full h-full object-cover" @error="handleImageError"
            :onerror="defaultAvatarUrl" />
    </div>
</template>

<script setup lang="ts">
// 定义props
interface Props {
    avatorUrl?: string;
}

const props = withDefaults(defineProps<Props>(), {
    avatorUrl: ''
});

// 默认头像的 base64 数据
const defaultAvatarUrl = "this.src='data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZpZXdCb3g9IjAgMCAyNCAyNCIgZmlsbD0iI2NjYyI+PHBhdGggZD0iTTEyIDJDNi40OCAyIDIgNi40OCAyIDEyczQuNDggMTAgMTAgMTAgMTAtNC40OCAxMC0xMFMxNy41MiAyIDEyIDJ6bTAgM2MyLjY3IDAgNC41NCAyLjE5IDQuNSA0Ljg4QzE2LjUgMTIuNTcgMTQuNDEgMTUgMTIgMTVzLTQuNS0yLjQzLTQuNS01LjEyQzcuNSA3LjE5IDkuMzggNSAxMiA1em0wIDEzLjVjLTIuOTcgMC01LjU4LTEuNDctNy4xNi0zLjcyQzYuMzggMTQuMDggOS44OCAxMyAxMiAxM3M1LjYzIDEuMDggNy4xNiAxLjc4Yy0xLjU5IDIuMjUtNC4yIDMuNzItNy4xNiAzLjcyeiIvPjwvc3ZnPg=='";

// 图片加载失败处理函数
const handleImageError = (e: Event) => {
    const img = e.target as HTMLImageElement;
    img.style.padding = '2px'; // 给默认图标添加一些内边距
};
</script>