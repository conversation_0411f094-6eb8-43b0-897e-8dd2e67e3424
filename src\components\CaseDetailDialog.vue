<template>
  <el-dialog 
    :model-value="modelValue"
    @update:model-value="$emit('update:modelValue', $event)"
    title="案例详情" 
    width="700px"
    :close-on-click-modal="false"
    class="case-detail-dialog"
  >
    <div v-if="caseData" class="case-detail max-h-[90vh] overflow-y-auto">
      <!-- 案例基本信息 -->
      <div class="flex items-start mb-4">
        <img 
          :src="caseData.imageUrl" 
          class="w-16 h-16 rounded-lg object-cover mr-3 flex-shrink-0" 
          :alt="caseData.name"
        />
        <div class="flex-grow">
          <h3 class="text-lg font-bold text-gray-800 mb-1">{{ caseData.name }}</h3>
          <p class="text-gray-600 text-sm mb-2 line-clamp-2">{{ caseData.description }}</p>
          <div class="flex items-center space-x-2">
            <el-tag :type="getStatusType(caseData.status)" size="small">
              {{ getStatusLabel(caseData.status) }}
            </el-tag>
            <el-tag :type="caseData.isPublic ? 'success' : 'info'" size="small">
              {{ caseData.isPublic ? '公开' : '私有' }}
            </el-tag>
          </div>
        </div>
      </div>
      
      <!-- 详细信息 -->
      <div class="grid grid-cols-1 md:grid-cols-2 gap-3 mb-4">
        <div class="bg-gray-50 rounded-lg p-3">
          <h4 class="font-medium text-gray-800 mb-2 text-sm">联系信息</h4>
          <div class="space-y-1">
            <div class="flex items-center text-sm">
              <el-icon class="text-gray-500 mr-2 text-xs"><User /></el-icon>
              <span class="text-gray-700">{{ caseData.contact }}</span>
            </div>
            <div class="flex items-center text-sm">
              <el-icon class="text-gray-500 mr-2 text-xs"><Phone /></el-icon>
              <span class="text-gray-700">{{ caseData.phone }}</span>
            </div>
          </div>
        </div>
        
        <div class="bg-gray-50 rounded-lg p-3">
          <h4 class="font-medium text-gray-800 mb-2 text-sm">申请信息</h4>
          <div class="space-y-1">
            <div class="flex items-center text-sm">
              <el-icon class="text-gray-500 mr-2 text-xs"><Calendar /></el-icon>
              <span class="text-gray-700">{{ caseData.submitTime }}</span>
            </div>
            <div class="flex items-center text-sm">
              <el-icon class="text-gray-500 mr-2 text-xs"><Document /></el-icon>
              <span class="text-gray-700 truncate">{{ caseData._id }}</span>
            </div>
          </div>
        </div>
      </div>
      
      <!-- 附件列表 -->
      <div v-if="caseData.attachments && caseData.attachments.length > 0">
        <h4 class="font-medium text-gray-800 mb-2 flex items-center text-sm">
          <el-icon class="mr-1 text-xs"><Paperclip /></el-icon>
          附件列表 ({{ caseData.attachments.length }})
        </h4>
        <div class="bg-gray-50 rounded-lg p-3">
          <div class="space-y-2 max-h-60 overflow-y-auto">
            <div 
              v-for="(attachment, index) in caseData.attachments" 
              :key="index"
              class="flex items-center justify-between p-2 bg-white rounded border border-gray-200 hover:border-indigo-300 transition-colors"
            >
              <div class="flex items-center flex-1 min-w-0">
                <el-icon class="text-blue-500 mr-2 text-sm flex-shrink-0">
                  <Document v-if="getFileIcon(attachment) === 'document'" />
                  <Picture v-else-if="getFileIcon(attachment) === 'image'" />
                  <VideoPlay v-else-if="getFileIcon(attachment) === 'video'" />
                  <Folder v-else />
                </el-icon>
                <div class="min-w-0 flex-1">
                  <div class="font-medium text-gray-800 text-sm truncate">{{ attachment }}</div>
                  <div class="text-xs text-gray-500">{{ getFileSize(attachment) }}</div>
                </div>
              </div>
              <el-button 
                type="primary" 
                size="small"
                class="ml-2 flex-shrink-0"
                @click="downloadAttachment(attachment)"
              >
                <el-icon><Download /></el-icon>
              </el-button>
            </div>
          </div>
        </div>
      </div>
      
      <!-- 无附件提示 -->
      <div v-else class="text-center py-4">
        <el-icon class="text-gray-400 text-2xl mb-1"><Document /></el-icon>
        <p class="text-gray-500 text-sm">暂无附件</p>
      </div>
    </div>
    
    <template #footer>
      <div class="flex justify-between items-center">
        <!-- 左侧状态操作 -->
        <div class="flex items-center space-x-3">
          <div class="flex items-center space-x-2">
            <span class="text-sm text-gray-600">审核状态：</span>
            <el-select 
              :model-value="caseData?.status" 
              size="small" 
              style="width: 120px"
              @change="handleStatusChange"
            >
              <el-option label="待审核" value="pending" />
              <el-option label="已通过" value="approved" />
              <el-option label="已拒绝" value="rejected" />
            </el-select>
          </div>
          <el-button 
            type="danger" 
            size="small" 
            plain
            @click="handleDelete"
          >
            <el-icon class="mr-1"><Delete /></el-icon>
            删除案例
          </el-button>
        </div>
        
        <!-- 右侧关闭按钮 -->
        <div class="flex space-x-2">
          <el-button @click="$emit('update:modelValue', false)">关闭</el-button>
          <el-button type="primary" @click="handleEdit">编辑案例</el-button>
        </div>
      </div>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup>
import { ElMessage } from 'element-plus'
import { useCaseStore, type CaseApplication } from '@/stores/caseStore'
import { 
  User, Phone, Calendar, Document, Paperclip, Download,
  Picture, VideoPlay, Folder, Delete
} from '@element-plus/icons-vue'

interface Props {
  modelValue: boolean
  caseData: CaseApplication | null
}

interface Emits {
  (e: 'update:modelValue', value: boolean): void
  (e: 'status-change', caseId: string, status: CaseApplication['status']): void
  (e: 'delete', caseId: string): void
  (e: 'edit', caseData: CaseApplication): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()
const caseStore = useCaseStore()

// 获取状态类型
const getStatusType = (status: string) => {
  switch (status) {
    case 'approved': return 'success'
    case 'pending': return 'warning'
    case 'rejected': return 'danger'
    default: return 'info'
  }
}

// 获取状态标签
const getStatusLabel = (status: string) => {
  switch (status) {
    case 'approved': return '已通过'
    case 'pending': return '待审核'
    case 'rejected': return '已拒绝'
    default: return '未知'
  }
}

// 获取文件图标类型
const getFileIcon = (fileName: string) => {
  const ext = fileName.split('.').pop()?.toLowerCase()
  if (['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp'].includes(ext || '')) {
    return 'image'
  } else if (['mp4', 'avi', 'mov', 'wmv', 'flv'].includes(ext || '')) {
    return 'video'
  } else if (['pdf', 'doc', 'docx', 'txt', 'rtf'].includes(ext || '')) {
    return 'document'
  }
  return 'file'
}

// 获取文件大小（模拟）
const getFileSize = (fileName: string) => {
  const sizes = ['1.2MB', '856KB', '2.3MB', '445KB', '1.8MB']
  return sizes[Math.floor(Math.random() * sizes.length)]
}

// 下载附件
const downloadAttachment = async (fileName: string) => {
  try {
    const success = await caseStore.downloadAttachment(
      `${fileName}`,
      fileName
    )
    if (success) {
      ElMessage.success(`正在下载 ${fileName}`)
    } else {
      ElMessage.error('下载失败')
    }
  } catch (error) {
    ElMessage.error('下载失败')
  }
}

// 处理状态变更
const handleStatusChange = (newStatus: string) => {
  if (!props.caseData) return
  emit('status-change', props.caseData._id, newStatus as CaseApplication['status'])
}

// 处理删除
const handleDelete = () => {
  if (!props.caseData) return
  emit('delete', props.caseData._id)
}

// 处理编辑
const handleEdit = () => {
  if (!props.caseData) return
  emit('edit', props.caseData)
}
</script>

<style scoped>
/* 案例详情对话框样式 */
.case-detail-dialog :deep(.el-dialog__body) {
  padding: 16px 20px;
}

.case-detail-dialog :deep(.el-dialog__footer) {
  padding: 12px 20px;
  border-top: 1px solid #f0f0f0;
}

/* 文本截断样式 */
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

/* 移动端底部按钮布局优化 */
@media (max-width: 768px) {
  .case-detail-dialog :deep(.el-dialog__footer) {
    padding: 12px 16px;
  }
  
  .case-detail-dialog .flex.justify-between {
    flex-direction: column;
    gap: 12px;
  }
  
  .case-detail-dialog .flex.justify-between > div {
    justify-content: center;
  }
  
  .case-detail-dialog .flex.justify-between > div:first-child {
    order: 2;
  }
  
  .case-detail-dialog .flex.justify-between > div:last-child {
    order: 1;
  }
}
</style> 