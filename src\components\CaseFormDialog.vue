<template>
  <el-dialog 
    :model-value="modelValue"
    @update:model-value="$emit('update:modelValue', $event)"
    :title="isEdit ? '编辑案例' : '新建案例申请'" 
    width="600px"
    :close-on-click-modal="false"
    @open="handleDialogOpen"
  >
    <el-form 
      ref="formRef"
      :model="formData" 
      :rules="formRules"
      label-width="100px"
    >
      <el-form-item label="方案名称" prop="name" required>
        <el-input v-model="formData.name" placeholder="请输入方案名称" />
      </el-form-item>
      
      <el-form-item label="方案图片" prop="imageUrl">
        <div class="flex items-center space-x-4 w-full">
          <el-input 
            v-model="formData.imageUrl" 
            placeholder="请输入图片URL" 
            style="flex: 1" 
          />
          <img 
            v-if="formData.imageUrl" 
            :src="formData.imageUrl" 
            class="w-16 h-16 rounded-lg object-cover" 
            @error="handleImageError"
          />
        </div>
      </el-form-item>
      
      <el-form-item label="方案描述" prop="description">
        <el-input 
          v-model="formData.description" 
          type="textarea" 
          :rows="3" 
          placeholder="请简要描述方案内容" 
        />
      </el-form-item>
      
      <el-form-item label="联系人" prop="contact" required>
        <el-input v-model="formData.contact" placeholder="请输入联系人姓名" />
      </el-form-item>
      
      <el-form-item label="联系方式" prop="phone" required>
        <el-input v-model="formData.phone" placeholder="请输入手机号码" />
      </el-form-item>
      
      <el-form-item label="公开设置">
        <el-switch 
          v-model="formData.isPublic" 
          active-text="公开展示" 
          inactive-text="仅内部可见"
        />
      </el-form-item>
      
      <el-form-item label="附件">
        <div class="w-full">
          <el-upload
            ref="uploadRef"
            class="upload-demo"
            drag
            action="#"
            multiple
            :auto-upload="false"
            :on-change="handleFileChange"
            :on-remove="handleFileRemove"
            :file-list="fileList"
            accept=".pdf,.doc,.docx,.jpg,.jpeg,.png,.gif,.mp4,.avi"
          >
            <el-icon class="el-icon--upload"><UploadFilled /></el-icon>
            <div class="el-upload__text">
              将文件拖到此处，或<em>点击上传</em>
            </div>
            <template #tip>
              <div class="el-upload__tip">
                支持 jpg/png/pdf/doc 等格式，单个文件不超过10MB
              </div>
            </template>
          </el-upload>
        </div>
      </el-form-item>
    </el-form>
    
    <template #footer>
      <div class="flex justify-end space-x-2">
        <el-button @click="handleCancel">取消</el-button>
        <el-button type="primary" @click="handleSubmit" :loading="submitting">
          {{ isEdit ? '保存修改' : '提交申请' }}
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup>
import { ref, reactive, watch } from 'vue'
import { ElMessage, type FormInstance, type FormRules, type UploadFile } from 'element-plus'
import { useCaseStore, type CaseApplication, type CaseForm } from '@/stores/caseStore'
import { UploadFilled } from '@element-plus/icons-vue'

interface Props {
  modelValue: boolean
  caseData?: CaseApplication | null
  isEdit?: boolean
}

interface Emits {
  (e: 'update:modelValue', value: boolean): void
  (e: 'submit', formData: CaseForm): void
}

const props = withDefaults(defineProps<Props>(), {
  isEdit: false,
  caseData: null
})

const emit = defineEmits<Emits>()
const caseStore = useCaseStore()

// 表单引用
const formRef = ref<FormInstance>()
const uploadRef = ref()
const submitting = ref(false)

// 表单数据
const formData = reactive<CaseForm>({
  name: '',
  imageUrl: '',
  description: '',
  contact: '',
  phone: '',
  isPublic: false,
  attachments: []
})

// 文件列表
const fileList = ref<UploadFile[]>([])

// 表单验证规则
const formRules: FormRules = {
  name: [
    { required: true, message: '请输入方案名称', trigger: 'blur' },
    { min: 2, max: 100, message: '方案名称长度在 2 到 100 个字符', trigger: 'blur' }
  ],
  contact: [
    { required: true, message: '请输入联系人姓名', trigger: 'blur' },
    { min: 2, max: 20, message: '联系人姓名长度在 2 到 20 个字符', trigger: 'blur' }
  ],
  phone: [
    { required: true, message: '请输入联系方式', trigger: 'blur' },
    { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号码', trigger: 'blur' }
  ],
  description: [
    { max: 500, message: '描述不能超过 500 个字符', trigger: 'blur' }
  ]
}

// 监听案例数据变化，用于编辑模式
watch(() => props.caseData, (newData) => {
  if (newData && props.isEdit) {
    Object.assign(formData, {
      name: newData.name,
      imageUrl: newData.imageUrl,
      description: newData.description,
      contact: newData.contact,
      phone: newData.phone,
      isPublic: newData.isPublic,
      attachments: [...newData.attachments]
    })
    
    // 更新文件列表显示
    fileList.value = newData.attachments.map((fileName, index) => ({
      name: fileName,
      uid: index,
      status: 'success' as const,
      url: `https://example.com/attachments/${fileName}`
    }))
  }
}, { immediate: true })

// 对话框打开时的处理
const handleDialogOpen = () => {
  if (!props.isEdit) {
    // 新建模式，重置表单
    resetForm()
  }
}

// 重置表单
const resetForm = () => {
  Object.assign(formData, {
    name: '',
    imageUrl: '',
    description: '',
    contact: '',
    phone: '',
    isPublic: false,
    attachments: []
  })
  fileList.value = []
  formRef.value?.clearValidate()
}

// 图片加载错误处理
const handleImageError = () => {
  ElMessage.warning('图片加载失败，请检查URL是否正确')
}

// 文件变更处理
const handleFileChange = (file: UploadFile) => {
  // 检查文件大小
  if (file.size && file.size > 10 * 1024 * 1024) {
    ElMessage.error('文件大小不能超过 10MB')
    return false
  }
  
  // 检查文件类型
  const allowedTypes = [
    'application/pdf',
    'application/msword',
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    'image/jpeg',
    'image/jpg',
    'image/png',
    'image/gif',
    'video/mp4',
    'video/avi'
  ]
  
  if (file.raw && !allowedTypes.includes(file.raw.type)) {
    ElMessage.error('不支持的文件类型')
    return false
  }
  
  // 添加到附件列表
  if (file.name && !formData.attachments.includes(file.name)) {
    formData.attachments.push(file.name)
  }
  
  return true
}

// 文件移除处理
const handleFileRemove = (file: UploadFile) => {
  const index = formData.attachments.findIndex(name => name === file.name)
  if (index > -1) {
    formData.attachments.splice(index, 1)
  }
}

// 取消操作
const handleCancel = () => {
  emit('update:modelValue', false)
  if (!props.isEdit) {
    resetForm()
  }
}

// 提交表单
const handleSubmit = async () => {
  if (!formRef.value) return
  
  try {
    // 表单验证
    await formRef.value.validate()
    
    submitting.value = true
    
    // 模拟文件上传
    const uploadedAttachments: string[] = []
    for (const file of fileList.value) {
      if (file.raw) {
        try {
          const url = await caseStore.uploadAttachment(file.raw)
          uploadedAttachments.push(file.name || `file_${Date.now()}`)
        } catch (error) {
          console.error('文件上传失败:', error)
          uploadedAttachments.push(file.name || `file_${Date.now()}`)
        }
      } else {
        // 已存在的文件
        uploadedAttachments.push(file.name || '')
      }
    }
    
    // 提交表单数据
    const submitData: CaseForm = {
      ...formData,
      attachments: uploadedAttachments.filter(Boolean)
    }
    
    emit('submit', submitData)
    
  } catch (error) {
    console.error('表单验证失败:', error)
  } finally {
    submitting.value = false
  }
}
</script>

<style scoped>
.upload-demo {
  width: 100%;
}

:deep(.el-upload-dragger) {
  width: 100%;
}

:deep(.el-upload__tip) {
  margin-top: 8px;
  color: #606266;
  font-size: 12px;
}

/* 移动端适配 */
@media (max-width: 768px) {
  :deep(.el-dialog) {
    width: 90% !important;
    margin: 5vh auto;
  }
  
  :deep(.el-form-item__label) {
    width: 80px !important;
  }
  
  .flex.items-center.space-x-4 {
    flex-direction: column;
    align-items: stretch;
    gap: 12px;
  }
  
  .flex.items-center.space-x-4 img {
    align-self: center;
  }
}
</style> 