<template>
    <div class="p-6">
        <GoodsChart v-if="detail.productViewType === 'chart'"></GoodsChart>
        <GoodsList v-else></GoodsList>
    </div>
</template>

<script setup lang="ts">
import { useDetailStore } from '@/stores/detailStore';
import GoodsChart from './GoodsChart.vue';
import GoodsList from './GoodsList.vue';
const detail = useDetailStore();
</script>

<style scoped>
.el-select {
    width: 100%;
}
</style>