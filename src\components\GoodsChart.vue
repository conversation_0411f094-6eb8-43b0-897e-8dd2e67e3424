<template>
    <div class="space-y-4 relative">

        <div class="absolute right-0 top-0">
            <el-dropdown trigger="hover" placement="bottom-end" popper-class="no-arrow" @command="handleCommand">
                <el-button type="primary" circle>
                    <el-icon>
                        <Setting />
                    </el-icon>
                </el-button>
                <template #dropdown>
                    <el-dropdown-menu>
                        <el-dropdown-item command="toggleLegend">
                            <div class="flex items-center gap-2">
                                <el-icon v-if="showLegend">
                                    <Check />
                                </el-icon>
                                <span :class="{ 'ml-5': !showLegend }">显示商品名称</span>
                            </div>
                        </el-dropdown-item>
                    </el-dropdown-menu>
                </template>
            </el-dropdown>
        </div>

        <!-- 控制面板 -->
        <div class="flex items-center gap-4">
            <div class="flex items-center gap-2">
                <el-button type="primary" circle @click="showProductSelector = true">
                    <el-icon>
                        <Filter />
                    </el-icon>
                </el-button>
            </div>
            <el-select v-model="goodsHistroy.selectedDimension" placeholder="选择维度" style="width: 156px;">
                <el-option v-for="dim in GoodsDimensions" :key="dim.value" :label="dim.label" :value="dim.value" />
            </el-select>
        </div>

        <!-- 图表区域 -->
        <div ref="productChartRef" style="height: 600px"></div>

        <!-- 商品选择弹窗 -->
        <el-dialog v-model="showProductSelector" :title="'选择商品(' + multipleSelection.length + ')'" width="800px">
            <div class="space-y-4">
                <el-table :data="sortedProducts" @sort-change="handleSortChange" height="400px"
                    @selection-change="handleSelectionChange">
                    <el-table-column type="selection" width="55" />
                    <el-table-column label="商品信息" min-width="200">
                        <template #default="scope">
                            <div class="flex items-center gap-2">
                                <div class="w-10 h-10 rounded overflow-hidden flex-shrink-0">
                                    <img :src="scope.row.thumb" class="w-full h-full object-cover" />
                                </div>
                                <span class="text-sm truncate">{{ scope.row.name }}</span>
                            </div>
                        </template>
                    </el-table-column>
                    <el-table-column label="曝光点击率" prop="goodsExposeToClickRate" sortable width="120">
                        <template #default="scope">
                            {{ (scope.row.goodsExposeToClickRate * 100).toFixed(1) }}%
                        </template>
                    </el-table-column>
                    <el-table-column label="曝光转化率" prop="goodsExposeToBuyRate" sortable width="120">
                        <template #default="scope">
                            {{ (scope.row.goodsExposeToBuyRate * 100).toFixed(1) }}%
                        </template>
                    </el-table-column>
                    <el-table-column label="点击转化率" prop="goodsClickToBuyRate" sortable width="120">
                        <template #default="scope">
                            {{ (scope.row.goodsClickToBuyRate * 100).toFixed(1) }}%
                        </template>
                    </el-table-column>
                    <el-table-column label="总成交额" prop="goodsTotalTurnover" sortable width="120">
                        <template #default="scope">
                            ¥{{ scope.row.goodsTotalTurnover.toLocaleString('zh-CN') }}
                        </template>
                    </el-table-column>
                </el-table>
            </div>
            <template #footer>
                <div class="flex justify-end gap-2">
                    <el-button @click="showProductSelector = false">取消</el-button>
                    <el-button type="primary" @click="handleConfirmSelection">确定</el-button>
                </div>
            </template>
        </el-dialog>
    </div>
</template>

<script setup lang="ts">
import { ref, watch, onMounted, onUnmounted } from 'vue'
import { useDetailStore } from '@/stores/detailStore';
import { useHistoryStore } from '@/stores/historyStore';


import * as echarts from 'echarts';
import type { EChartsType } from 'echarts';
import { Filter, Setting, Check } from '@element-plus/icons-vue'
import { GoodsDimensions } from "@/comm/const"


const detail = useDetailStore();
const goodsHistroy = useHistoryStore();

const productChartRef = ref<HTMLElement>();
let chartInstance: EChartsType;

// 新增的状态和方法
const showProductSelector = ref(false);
const sortedProducts = ref<any[]>([]);
const multipleSelection = ref<any[]>([]);

// 初始化排序后的商品列表
watch(() => detail.productData, (newData) => {
    if (newData) {
        sortedProducts.value = [...newData];
    }
}, { immediate: true });

// 处理表格排序
const handleSortChange = ({ prop, order }: { prop: string, order: string | null }) => {
    if (!prop || !order) {
        sortedProducts.value = [...detail.productData];
        return;
    }
    sortedProducts.value.sort((a, b) => {
        if (order === 'ascending') {
            return a[prop] - b[prop];
        } else {
            return b[prop] - a[prop];
        }
    });
};

// 处理表格选择
const handleSelectionChange = (selection: any[]) => {
    multipleSelection.value = selection;
};

// 确认选择
const handleConfirmSelection = () => {
    // 获取当前表格选中的商品ID
    const selectedIds = multipleSelection.value.map(item => item.id);

    console.log("selected=>", selectedIds.length)

    // 合并现有选择和新选择，去重
    const newSelection = Array.from(new Set([...selectedIds]));
    goodsHistroy.selectedProducts = newSelection;
    showProductSelector.value = false;
};


// 添加图例显示控制
const showLegend = ref(false);

// 处理下拉菜单命令
const handleCommand = (command: string) => {
    if (command === 'toggleLegend' && chartInstance) {
        showLegend.value = !showLegend.value;
        const option = {
            legend: showLegend.value ? {
                data: goodsHistroy.selectedProducts.map(id =>
                    detail.productData.find(p => p.id === id)?.name
                ),
                top: 0,
                show: true,
            } : {
                show: false
            },
            grid: {
                left: '3%',
                right: '4%',
                bottom: '15%',
                top: showLegend.value ? '25%' : '5%',  // 根据是否显示图例调整顶部间距
                containLabel: true
            }
        };
        chartInstance.setOption(option);
    }
};

// 更新图表
const updateChart = async () => {
    if (!chartInstance || !detail.productData || !detail.roomDetail) return;

    console.log("updateChart========>", goodsHistroy.series.length)

    const option = {
        tooltip: {
            trigger: 'axis',
            formatter: function (params: any) {
                let result = `${params[0].axisValue}<br/>`;
                params.forEach((param: any) => {
                    const product = detail.productData.find(p => p.name === param.seriesName) as any;
                    if (!product) return;
                    result += `${param.marker}${param.seriesName}<br/>`;
                    GoodsDimensions.forEach(dim => {
                        const value = product[dim.value];
                        result += `${dim.label}: ${dim.value.includes('Rate')
                            ? (value * 100).toFixed(1) + '%'
                            : value.toLocaleString('zh-CN')
                            }<br/>`;
                    });
                });
                return result;
            }
        },
        legend: showLegend.value ? {
            data: goodsHistroy.selectedProducts.map(id =>
                detail.productData.find(p => p.id === id)?.name
            ),
            top: 0
        } : false,
        grid: {
            left: '3%',
            right: '4%',
            bottom: '15%',
            top: showLegend.value ? '25%' : '5%',  // 根据是否显示图例调整顶部间距
            containLabel: true
        },
        xAxis: {
            type: 'category',
            data: goodsHistroy.xAxisData,
            boundaryGap: false
        },
        yAxis: {
            type: 'value',
            axisLabel: {
                formatter: (value: number) =>
                    goodsHistroy.selectedDimension.includes('Rate')
                        ? (value * 100).toFixed(1) + '%'
                        : value.toLocaleString('zh-CN')
            }
        },
        dataZoom: [
            {
                type: 'slider',
                show: true,
                xAxisIndex: [0],
                start: 0,
                end: 100
            },
            {
                type: 'inside',
                xAxisIndex: [0],
                start: 0,
                end: 100
            }
        ],
        series: goodsHistroy.series
    };
    chartInstance.setOption(option, true);
};


// 监听数据变化
watch([() => goodsHistroy.version], () => {
    updateChart()
}, { deep: true, immediate: true });


const echarResize = () => {
    if (!chartInstance.isDisposed()) chartInstance?.resize();
}

onMounted(() => {
    if (productChartRef.value) {
        chartInstance = echarts.init(productChartRef.value);
        // 监听窗口大小变化
        window.addEventListener('resize', echarResize);
    }
});

onUnmounted(() => {
    chartInstance?.dispose();
    window.removeEventListener('resize', echarResize);
});
</script>

<style>
.el-dialog__body {
    padding-top: 10px;
}

.el-dropdown-menu__item {
    padding: 5px 20px;
}

/* 去掉下拉框箭头 */
.no-arrow .el-popper__arrow {
    display: none !important;
}
</style>
