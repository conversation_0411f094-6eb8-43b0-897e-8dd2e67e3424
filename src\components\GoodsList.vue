<template>
    <div>
        <el-table :data="tableData" @sort-change="handleSortChange" stripe :border="false" class="w-full"
            :row-key="(row: any) => row.id">
            <el-table-column label="序号" width="80" prop="goodsCartNum" sortable>
                <template #default="scope">
                    <div class="flex items-center">
                        <span class="text-sm">{{ scope.row.goodsCartNum }}</span>
                    </div>
                </template>
            </el-table-column>

            <el-table-column label="商品信息" mini-width="300">
                <template #default="scope">
                    <div class="flex items-center gap-2">
                        <div class="w-10 h-10 rounded overflow-hidden flex-shrink-0">
                            <img :src="scope.row.thumb" class="w-full h-full object-cover" />
                        </div>
                        <span class="text-sm truncate">{{ scope.row.name }}</span>
                    </div>
                </template>
            </el-table-column>
            <el-table-column label="曝光点击率" width="180" prop="goodsExposeToClickRate" sortable>
                <template #default="scope">
                    <div class="flex items-center">
                        <span class="text-sm">{{ (scope.row.goodsExposeToClickRate * 100).toFixed(1) }}%</span>
                        <ValueChange :current="scope.row.goodsExposeToClickRate"
                            :previous="previousData[scope.row.id]?.goodsExposeToClickRate" :is-percentage="true" />
                    </div>
                </template>
            </el-table-column>
            <el-table-column label="曝光转化率" width="180" prop="goodsExposeToBuyRate" sortable>
                <template #default="scope">
                    <div class="flex items-center">
                        <span class="text-sm">{{ (scope.row.goodsExposeToBuyRate * 100).toFixed(1) }}%</span>
                        <ValueChange :current="scope.row.goodsExposeToBuyRate"
                            :previous="previousData[scope.row.id]?.goodsExposeToBuyRate" :is-percentage="true" />
                    </div>
                </template>
            </el-table-column>
            <el-table-column label="点击转化率" width="180" prop="goodsClickToBuyRate" sortable>
                <template #default="scope">
                    <div class="flex items-center">
                        <span class="text-sm">{{ (scope.row.goodsClickToBuyRate * 100).toFixed(1) }}%</span>
                        <ValueChange :current="scope.row.goodsClickToBuyRate"
                            :previous="previousData[scope.row.id]?.goodsClickToBuyRate" :is-percentage="true" />
                    </div>
                </template>
            </el-table-column>
            <el-table-column label="总成交额" width="180" prop="goodsTotalTurnover" sortable>
                <template #default="scope">
                    <div class="flex items-center">
                        <span class="font-medium">¥{{ scope.row.goodsTotalTurnover.toLocaleString('zh-CN') }}</span>
                        <ValueChange :current="scope.row.goodsTotalTurnover"
                            :previous="previousData[scope.row.id]?.goodsTotalTurnover" :is-currency="true" />
                    </div>
                </template>
            </el-table-column>
            <el-table-column label="退款率" width="180" prop="goodsRefundRate" sortable>
                <template #default="scope">
                    <div class="flex items-center">
                        <span :class="[
                            'text-xs px-2 py-1 rounded',
                            scope.row.goodsRefundRate > 0.1 ? 'text-red-600 bg-red-50' : 'text-green-600 bg-green-50'
                        ]">{{ (scope.row.goodsRefundRate * 100).toFixed(1) }}%</span>
                        <ValueChange :current="scope.row.goodsRefundRate"
                            :previous="previousData[scope.row.id]?.goodsRefundRate" :is-percentage="true"
                            :inverse="true" />
                    </div>
                </template>
            </el-table-column>
        </el-table>
    </div>
</template>

<script setup lang="tsx">
import { ref, watch, computed, onMounted, defineComponent } from 'vue'
import { useDetailStore } from '@/stores/detailStore';
import { useNatsStore } from '@/stores/natsStore';


const ValueChange = defineComponent({
    props: {
        current: Number,
        previous: Number,
        isPercentage: Boolean,
        isCurrency: Boolean,
        inverse: Boolean
    },
    setup(props) {
        const diff = computed(() => {
            if (props.current === undefined || props.previous === undefined) return null;
            return props.current - props.previous;
        });

        const formattedDiff = computed(() => {
            if (diff.value === null) return '';

            // 如果差异太小，不显示
            if (Math.abs(diff.value) < 0.001) return '';

            const prefix = diff.value > 0 ? '↑ ' : '↓ ';

            if (props.isPercentage) {
                // 对于百分比，显示百分点变化
                const percentChange = (diff.value * 100).toFixed(3);
                return `${prefix}${percentChange}%`;
            } else if (props.isCurrency) {
                // 对于货币，显示整数变化
                return `${prefix}¥${Math.abs(diff.value).toLocaleString('zh-CN')}`;
            } else {
                // 默认显示
                return `${prefix}${diff.value.toFixed(2)}`;
            }
        });

        const changeClass = computed(() => {
            if (diff.value === null) return '';

            const isPositive = props.inverse ? diff.value < 0 : diff.value > 0;
            const isNegative = props.inverse ? diff.value > 0 : diff.value < 0;

            if (isPositive) {
                return 'ml-2 text-xs text-green-600';
            } else if (isNegative) {
                return 'ml-2 text-xs text-red-600';
            }
            return '';
        });

        return () => {
            if (!formattedDiff.value) return null;

            return (
                <span class={changeClass.value} >
                    {formattedDiff.value}
                </span>
            );
        };
    }
});


const detail = useDetailStore();
const nats = useNatsStore();
const tableData = ref<any>([]);
const previousData = ref<Record<string, any>>({});

// 保存上一次的数据状态，用于比较变化
const savePreviousData = () => {
    const dataMap: Record<string, any> = {};
    tableData.value.forEach((item: any) => {
        if (item.id) {
            dataMap[item.id] = { ...item };
        }
    });
    previousData.value = dataMap;
};

// 监听原始数据变化
watch(() => detail.productData, (newData) => {
    // 保存当前数据用于比较
    savePreviousData();

    // 更新表格数据
    tableData.value = [...newData];
}, { immediate: true });

// 监听实时数据更新
watch(() => nats.goodDirty, () => {
    // 当有实时数据更新时，保存当前状态用于比较
    savePreviousData();
}, { immediate: true });

// 排序处理函数
//@ts-ignore
const handleSortChange = ({ prop, order }) => {
    if (!prop || !order) {
        tableData.value = [...detail.productData];
        return;
    }

    //@ts-ignore
    tableData.value.sort((a, b) => {
        if (order === 'ascending') {
            return a[prop] - b[prop];
        } else {
            return b[prop] - a[prop];
        }
    });
};
</script>

<style scoped>
.el-select {
    width: 100%;
}

/* 添加行移动动画 */
.el-table .el-table__row {
    transition: all 0.5s ease;
}

/* 值变化动画 */
.text-green-600,
.text-red-600 {
    animation: fadeIn 1s;
}

@keyframes fadeIn {
    from {
        opacity: 0;
    }

    to {
        opacity: 1;
    }
}
</style>
