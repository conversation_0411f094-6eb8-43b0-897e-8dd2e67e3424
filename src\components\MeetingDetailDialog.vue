<template>
  <el-dialog
    v-model="visible"
    title="会议详情"
    width="600px"
    :before-close="handleClose"
  >
    <div v-if="meeting" class="meeting-detail">
      <el-descriptions :column="1" border>
        <el-descriptions-item label="会议ID">
          {{ meeting.id }}
        </el-descriptions-item>
        <el-descriptions-item label="会议名称">
          {{ meeting.name }}
        </el-descriptions-item>
        <el-descriptions-item label="会议类型" v-if="meeting.meetType">
          <el-tag size="small">{{ meeting.meetType }}</el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="参与者" v-if="meeting.users">
          {{ meeting.users }}
        </el-descriptions-item>
        <el-descriptions-item label="文件大小" v-if="meeting.audioOriginSize">
          {{ formatFileSize(meeting.audioOriginSize) }}
        </el-descriptions-item>
        <el-descriptions-item label="时长" v-if="meeting.audioDuration">
          {{ formatDuration(meeting.audioDuration) }}
        </el-descriptions-item>
        <el-descriptions-item label="会议描述" v-if="meeting.desc">
          <div class="max-h-20 overflow-y-auto">
            {{ meeting.desc }}
          </div>
        </el-descriptions-item>
        <el-descriptions-item label="音频状态">
          <el-tag 
            :type="getAudioStateType(meeting.audioState)" 
            size="small"
          >
            {{ getAudioStateText(meeting.audioState) }}
          </el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="创建时间">
          {{ formatTime(meeting.created_at) }}
        </el-descriptions-item>
        <el-descriptions-item label="更新时间" v-if="meeting.updated_at">
          {{ formatTime(meeting.updated_at) }}
        </el-descriptions-item>
      </el-descriptions>
    </div>
    
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">关闭</el-button>
        <el-button 
          type="primary" 
          @click="handlePreview"
          :disabled="!canPreview"
        >
          预览识别结果
        </el-button>
        <el-button 
          type="warning" 
          :icon="Refresh"
          @click="handleRetry"
          :disabled="!canRetry"
        >
          重新转换
        </el-button>
        <el-button 
          type="danger" 
          :icon="Delete"
          @click="handleDelete"
        >
          删除会议
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { Delete, Refresh } from '@element-plus/icons-vue'
import { ElMessageBox, ElMessage } from 'element-plus'
import type { MeetingResponse } from '@/api/meetingApi'

interface Props {
  modelValue: boolean
  meeting?: MeetingResponse | null
}

interface Emits {
  (e: 'update:modelValue', value: boolean): void
  (e: 'preview', meeting: MeetingResponse): void
  (e: 'delete', meeting: MeetingResponse): void
  (e: 'retry', meeting: MeetingResponse): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

// 是否可以预览
const canPreview = computed(() => {
  return props.meeting?.audioState === 'finished'
})

// 是否可以重新转换（状态不是处理中）
const canRetry = computed(() => {
  return props.meeting?.audioState !== 'handing'
})

// 关闭弹窗
const handleClose = () => {
  visible.value = false
}

// 预览识别结果
const handlePreview = () => {
  if (props.meeting) {
    emit('preview', props.meeting)
    handleClose()
  }
}

// 删除会议
const handleDelete = async () => {
  if (!props.meeting) return
  
  try {
    await ElMessageBox.confirm(
      `确定要删除会议"${props.meeting.name}"吗？此操作不可撤销。`,
      '确认删除',
      {
        confirmButtonText: '确定删除',
        cancelButtonText: '取消',
        type: 'warning',
        confirmButtonClass: 'el-button--danger'
      }
    )
    
    emit('delete', props.meeting)
    handleClose()
    
  } catch (error: any) {
    // 用户取消删除，不做任何操作
  }
}

// 重新转换
const handleRetry = async () => {
  if (!props.meeting) return
  
  try {
    await ElMessageBox.confirm(
      `确定要重新转换会议"${props.meeting.name}"吗？这将重新开始音频处理流程。`,
      '确认重新转换',
      {
        confirmButtonText: '确定转换',
        cancelButtonText: '取消',
        type: 'info'
      }
    )
    
    emit('retry', props.meeting)
    handleClose()
    
  } catch (error: any) {
    // 用户取消重新转换，不做任何操作
  }
}

// 转换音频状态
const getAudioFileStatus = (audioState?: string | null): 'uploaded' | 'processing' | 'done' | 'error' => {
  switch (audioState) {
    case 'uploaded': return 'uploaded'
    case 'handing': return 'processing'  // 处理中
    case 'finished': return 'done'       // 已完成
    case 'canceled':
    case 'failed': return 'error'        // 取消或失败
    default: return 'uploaded'
  }
}

// 获取状态类型
const getStatusType = (status: string) => {
  switch (status) {
    case 'uploaded': return 'success'
    case 'processing': return 'warning'
    case 'done': return 'primary'
    case 'error': return 'danger'
    default: return 'info'
  }
}

// 获取状态文本
const getStatusText = (status: string) => {
  switch (status) {
    case 'uploaded': return '已上传'
    case 'processing': return '处理中'
    case 'done': return '已完成'
    case 'error': return '错误'
    default: return '未知'
  }
}

// 获取音频状态显示文本
const getAudioStateText = (audioState?: string | null) => {
  return getStatusText(getAudioFileStatus(audioState))
}

// 获取音频状态显示类型
const getAudioStateType = (audioState?: string | null) => {
  return getStatusType(getAudioFileStatus(audioState))
}

// 格式化时间
const formatTime = (timeStr?: string | null) => {
  if (!timeStr) return '-'
  return new Date(timeStr).toLocaleString('zh-CN')
}

// 格式化文件大小（字节转MB）
const formatFileSize = (bytes: number) => {
  if (!bytes || bytes === 0) return '-'
  const mb = bytes / (1024 * 1024)
  return `${mb.toFixed(2)} MB`
}

// 格式化时长（秒转分:秒）
const formatDuration = (seconds: number) => {
  if (!seconds || seconds === 0) return '-'
  const minutes = Math.floor(seconds / 60)
  const remainingSeconds = seconds % 60
  return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`
}
</script>

<style scoped>
.meeting-detail {
  max-height: 60vh;
  overflow-y: auto;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}
</style> 