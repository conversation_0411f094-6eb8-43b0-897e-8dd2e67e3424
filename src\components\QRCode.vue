<template>
  <div class="qrcode-container relative">
    <!-- 二维码显示区域 -->
    <div class="qrcode-display flex items-center justify-center bg-white p-2 rounded-lg">
      <canvas ref="qrcodeRef" :width="props.size" :height="props.size"></canvas>
    </div>
    
    <!-- 失效遮罩层 -->
    <div v-if="isExpired" class="absolute inset-0 bg-black/60 rounded-lg flex flex-col items-center justify-center">
      <span class="text-white text-sm mb-2">二维码已失效</span>
      <el-button 
        type="primary" 
        size="small" 
        class="!rounded-button" 
        @click="refreshQRCode"
      >
        <el-icon class="mr-1"><Refresh /></el-icon>
        刷新
      </el-button>
    </div>
    
    <!-- 倒计时显示 -->
    <div class="text-xs text-gray-500 text-center mt-1" v-if="!isExpired">
      有效期: {{ formatTime(remainingTime) }}
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onBeforeUnmount } from 'vue';
import { Refresh } from '@element-plus/icons-vue';
//@ts-ignore
import QRCodeLib from 'qrcode';

const props = defineProps({
  value: {
    type: String,
    required: true,
    default: 'https://example.com'
  },
  expiryTime: {
    type: Number,
    default: 10 // 默认120秒有效期
  },
  size: {
    type: Number,
    default: 150
  }
});

const qrcodeRef = ref<HTMLCanvasElement | null>(null);
const isExpired = ref(false);
const remainingTime = ref(props.expiryTime);
let timer: number | null = null;

// 生成二维码
const generateQRCode = () => {
  if (!qrcodeRef.value) return;
  
  QRCodeLib.toCanvas(qrcodeRef.value, props.value, {
    width: props.size,
    margin: 1,
    color: {
      dark: '#000',
      light: '#fff'
    }
    //@ts-ignore
  }, (error) => {
    if (error) console.error('二维码生成失败:', error);
  });
  
  // 重置状态
  isExpired.value = false;
  remainingTime.value = props.expiryTime;
  
  // 启动倒计时
  startTimer();
};

// 刷新二维码
const refreshQRCode = () => {
  generateQRCode();
};

// 启动倒计时
const startTimer = () => {
  // 清除现有定时器
  if (timer) {
    clearInterval(timer);
  }
  
  // 设置新定时器
  timer = setInterval(() => {
    if (remainingTime.value <= 0) {
      isExpired.value = true;
      if (timer) clearInterval(timer);
    } else {
      remainingTime.value--;
    }
  }, 1000) as unknown as number;
};

// 格式化时间显示
const formatTime = (seconds: number) => {
  const mins = Math.floor(seconds / 60);
  const secs = seconds % 60;
  return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
};

// 组件挂载时生成二维码
onMounted(() => {
  generateQRCode();
});

// 组件卸载前清除定时器
onBeforeUnmount(() => {
  if (timer) {
    clearInterval(timer);
  }
});
</script>

<style scoped>
.qrcode-container {
  display: inline-block;
}

.qrcode-display {
  width: v-bind('props.size + "px"');
  height: v-bind('props.size + "px"');
}
</style>
