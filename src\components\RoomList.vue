<template>
    <div class="w-[320px] bg-white border-r border-gray-200 h-full flex flex-col">
        <!-- 固定在顶部的搜索区域 -->
        <div class="p-4 border-b border-gray-200">
            <div class="flex items-center gap-2">
                <el-select v-model="searchText" clearable filterable placeholder="搜索主播" class="!rounded-button flex-1"
                    @change="changedUser">
                    <el-option v-for="anchor in userList.list" :key="anchor.userId" :label="anchor.userName"
                        :value="anchor.userId" />
                </el-select>
                <el-button class="!rounded-button whitespace-nowrap flex items-center" @click="refreshList">
                    <el-icon :class="{ 'animate-spin': liveList.loading }">
                        <Refresh />
                    </el-icon>
                </el-button>
            </div>
        </div>

        <!-- 可滚动的列表区域 -->
        <div class="flex-1 overflow-y-auto p-4">
            <div
                class="relative space-y-4 before:content-[''] before:absolute before:left-[21px] before:top-0 before:bottom-0 before:w-[2px] before:bg-gray-200">
                <div v-for="room in liveList.list" :key="room._id" :data-room-id="room._id" :class="[
                    'p-3 rounded-lg cursor-pointer transition-all relative border',
                    liveList.currSelId === room._id ? 'bg-blue-50 border-blue-200 shadow-md scale-[1.02]' : 'hover:bg-gray-50 border-transparent'
                ]" @click="selectRoom(room._id)">
                    <div class="absolute left-[-12px] top-[30px] w-3 h-3 rounded-full border-2 border-white"
                        :class="[room.status == 1 ? 'bg-red-500' : 'bg-gray-500']"></div>
                    <div class="flex items-center justify-between mb-2">
                        <div class="flex items-center gap-3 ml-4">
                            <Avatar :avator-url="room.userAvator"> </Avatar>
                            <div>
                                <span :class="[
                                    'px-2 py-1 rounded text-xs',
                                    room.status == 1 ? 'bg-red-500 text-white' : 'bg-gray-500 text-white'
                                ]">
                                    {{ room.status == 1 ? '直播中' : '未开播' }}
                                </span>
                            </div>
                        </div>
                        <div class="flex flex-col items-end">
                            <div class="text-blue-600 font-medium">¥{{ room.roomCoreData.roomPayAmt }}</div>
                            <div class="text-sm text-gray-500">{{ room.roomCoreData.roomPayAmt }}</div>
                        </div>
                    </div>
                    <div class="flex flex-col gap-2 ml-4">
                        <span class="font-medium truncate">{{ room.userName }}</span>
                        <div class="flex items-center gap-4">
                            <span class="text-sm text-gray-500">{{ formatTime(room.startAt) }}</span>
                            <span class="text-sm text-gray-500">{{ room.status == 2 ? "duration" : '-' }}</span>
                        </div>
                    </div>
                </div>

                <!-- 底部提示文本 -->
                <div class="text-center py-2 mb-24 text-sm text-gray-400">
                    <template v-if="liveList.loading">
                        加载中...
                    </template>
                    <template v-else>
                        {{ liveList.canLoadNext ? '上拉加载更多' : '已经到底了' }}
                    </template>
                </div>
            </div>
        </div>
    </div>
</template>

<style scoped>
/* 自定义滚动条样式 */
.overflow-y-auto::-webkit-scrollbar {
    width: 6px;
}

.overflow-y-auto::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
}

.overflow-y-auto::-webkit-scrollbar-thumb {
    background: #888;
    border-radius: 3px;
}

.overflow-y-auto::-webkit-scrollbar-thumb:hover {
    background: #555;
}
</style>


<script lang="ts" setup>
import { ref, onMounted, onUnmounted } from 'vue';
import { Refresh } from '@element-plus/icons-vue';
import { useRoomList } from "@/stores/roomList"
import { useUserList } from '@/stores/userList';
import moment from 'moment';
import Avatar from './Avatar.vue';


// 设置中文
moment.locale('zh-cn');
// 格式化时间
const formatTime = (time: string) => {
    if (!time) return '-';
    return moment(time).format('YYYY-MM-DD HH:mm:ss');
}

const liveList = useRoomList();
const userList = useUserList();

const searchText = ref('');

// 添加滚动加载更多的逻辑
const handleScroll = async (e: Event) => {
    const target = e.target as HTMLElement;
    // 距离底部 20px 时触发加载
    console.log("handleScroll==>", target.scrollHeight - target.scrollTop - target.clientHeight);
    if (target.scrollHeight - target.scrollTop - target.clientHeight < 10) {
        if (!liveList.loading && liveList.canLoadNext) {
            await liveList.loadNextPage();
        }
    }
};

onMounted(() => {
    liveList.loadPage(1);
    userList.load("");
    // 添加滚动监听
    const scrollContainer = document.querySelector('.overflow-y-auto');
    if (scrollContainer) {
        scrollContainer.addEventListener('scroll', handleScroll);
    }
});

// 可选：添加组件卸载时的清理
onUnmounted(() => {
    const scrollContainer = document.querySelector('.overflow-y-auto');
    if (scrollContainer) {
        scrollContainer.removeEventListener('scroll', handleScroll);
    }
});

const changedUser = (userId: string) => {
    console.log("changedUser", userId);
    liveList.currUserId = userId;
    liveList.loadPage(1);
}

// 刷新列表
const refreshList = () => {
    liveList.fresh()
};

// 选择直播间
const selectRoom = (roomId: string) => {
    liveList.currSelId = roomId;
    // 获取当前选中的元素
    const selectedElement = document.querySelector(`[data-room-id="${roomId}"]`);
    if (selectedElement) {
        // 使用 scrollIntoView 将选中元素滚动到视图中间
        selectedElement.scrollIntoView({
            behavior: 'smooth',
            block: 'center'
        });
    }
};
</script>
