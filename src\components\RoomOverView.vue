<template>
    <div class="p-6 grid grid-cols-4 gap-4 grid-flow-row">
        <div v-for="(stat, index) in detail.overviewStats" :key="index"
            class="p-4 rounded-lg bg-gray-50 hover:bg-gray-100 transition-all">
            <div class="text-sm text-gray-500 mb-1">{{ stat.label }}</div>
            <div class="flex items-center gap-2">
                <div class="text-xl font-semibold">
                    <!-- Use CountUp for non-duration types -->
                    <template v-if="stat.type !== 'duration'">
                        <CountUp :end-val="getDisplayValue(stat.value, stat.type)" :options="countOptions(stat.type)" />
                    </template>
                    <!-- Use custom format for duration -->
                    <template v-else>
                        {{ formatValue(stat.value, 'duration') }}
                    </template>
                </div>
                
                <ValueChange 
                    :current="stat.value" 
                    :previous="previousValues[index]" 
                    :is-percentage="stat.type === 'percent'" 
                    :is-currency="stat.type === 'money'" 
                />
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import { ref, watch, onMounted } from 'vue'
import { useDetailStore } from '@/stores/detailStore';
import { formatValue } from "@/comm/utils"
import CountUp  from 'vue-countup-v3';
import ValueChange from '@/components/ValueChange';

const detail = useDetailStore();
const previousValues = ref<number[]>([]);

// Convert values for display based on type
const getDisplayValue = (value: number, type: string) => {
    if (type === 'percent') {
        return value * 100; // Convert to percentage for display
    }
    return value;
};

// CountUp options based on stat type
const countOptions = (type: string) => {
    const baseOptions = {
        duration: 1.5,
        useEasing: true,
        useGrouping: true,
    };
    
    switch (type) {
        case 'money':
            return { ...baseOptions, prefix: '¥', decimalPlaces: 2 };
        case 'percent':
            return { ...baseOptions, suffix: '%', decimalPlaces: 2 };
        case 'number':
            return { ...baseOptions, decimalPlaces: 0 };
        default:
            return baseOptions;
    }
};

// Watch for changes in the stats and update previous values
watch(() => detail.overviewStats, (newStats, oldStats) => {
    if (oldStats && oldStats.length) {
        // Store previous values when stats change
        previousValues.value = oldStats.map(stat => stat.value);
    } else if (newStats && newStats.length && previousValues.value.length === 0) {
        // Initialize previous values array with current values if empty
        previousValues.value = newStats.map(stat => stat.value);
    }
}, { deep: true });

// Initialize previous values on component mount
onMounted(() => {
    if (detail.overviewStats.length && previousValues.value.length === 0) {
        previousValues.value = detail.overviewStats.map(stat => stat.value);
    }
});
</script>
