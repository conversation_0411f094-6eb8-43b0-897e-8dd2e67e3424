<template>
  <div class="absolute right-0 top-12 w-64 bg-white rounded-lg shadow-lg z-1000 p-4 sort-panel">
    <div class="text-sm text-gray-500 mb-3">拖拽下方模块进行排序</div>
    <draggable v-model="moduleList" item-key="id" :animation="150" class="space-y-2" ghost-class="opacity-50"
      @end="handleSortEnd">
      <template #item="{ element }">
        <div class="p-3 rounded cursor-move transition-colors flex items-center gap-2" :class="[
          currId && element.id === currId
            ? 'bg-blue-50 hover:bg-blue-100'
            : 'bg-gray-50 hover:bg-gray-100'
        ]">
          <el-icon :class="[
            'text-gray-400',
            currId && element.id === currId && 'text-blue-500'
          ]">
            <Operation />
          </el-icon>
          <span :class="{ 'text-blue-600 font-medium': element.id === currId }">
            {{ element.title }}
          </span>
          <!-- 添加当前标识 -->
          <span v-if="currId && element.id === currId"
            class="ml-auto text-xs px-1.5 py-0.5 rounded bg-blue-100 text-blue-600">
            当前
          </span>
        </div>
      </template>
    </draggable>
  </div>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue'
import draggable from 'vuedraggable/src/vuedraggable'
import { Operation } from '@element-plus/icons-vue'

const props = defineProps<{
  modules: Array<{ id: string; title: string }>
  currId?: string  // 添加当前模块ID属性
}>()

const emit = defineEmits<{
  (e: 'update:modules', value: Array<{ id: string; title: string }>): void
  (e: 'sort-end'): void
}>()

const moduleList = ref(props.modules)

watch(moduleList, (newVal) => {
  emit('update:modules', newVal)
})

const handleSortEnd = () => {
  emit('sort-end')
}
</script>
