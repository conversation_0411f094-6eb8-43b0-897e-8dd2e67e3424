<template>
  <el-dialog
    v-model="visible"
    title="上传会议音视频"
    width="800px"
    :close-on-click-modal="false"
    destroy-on-close
  >
    <el-form ref="formRef" :model="form" :rules="rules" label-width="120px">
      <!-- 会议基础信息 -->
      <el-form-item label="会议名称" prop="name">
        <el-input v-model="form.name" placeholder="请输入会议名称" />
      </el-form-item>
      
      <el-form-item label="会议类型" prop="meetType">
        <el-select v-model="form.meetType" placeholder="请选择会议类型" clearable>
          <el-option label="内部会议" value="internal" />
          <el-option label="客户会议" value="customer" />
          <el-option label="项目会议" value="project" />
          <el-option label="培训会议" value="training" />
        </el-select>
      </el-form-item>

      <el-form-item label="会议时间" prop="meet_at">
          
      </el-form-item>

      <el-form-item label="参会人员" prop="users">
        <el-input
          v-model="form.users"
          type="textarea"
          placeholder="请输入参会人员，多人用逗号分隔"
          :rows="2"
        />
      </el-form-item>

      <el-form-item label="会议描述" prop="desc">
        <el-input
          v-model="form.desc"
          type="textarea"
          placeholder="请输入会议描述"
          :rows="3"
        />
      </el-form-item>

      <!-- 音视频文件选择 -->
      <el-form-item label="音视频文件" prop="audioFiles">
        <div class="w-full">
          <el-upload
            ref="uploadRef"
            class="upload-demo"
            drag
            :auto-upload="false"
            :show-file-list="false"
            multiple
            accept="audio/*,video/*"
            :on-change="handleFileChange"
          >
            <el-icon class="el-icon--upload"><upload-filled /></el-icon>
            <div class="el-upload__text">
              拖拽音视频文件到此处或 <em>点击选择</em>
            </div>
            <template #tip>
              <div class="el-upload__tip">
                支持音频和视频文件，多选文件格式必须一致（如都是.mp3或都是.mp4）
              </div>
            </template>
          </el-upload>

          <!-- 音视频文件列表 -->
          <div v-if="mediaFileList.length > 0" class="mt-4">
            <div class="text-sm text-gray-600 mb-2">
              已选择 {{ mediaFileList.length }} 个{{ getFileTypeText() }}文件（可拖拽调整顺序）：
            </div>
            <draggable
              v-model="mediaFileList"
              item-key="uid"
              class="space-y-2"
              @start="drag = true"
              @end="drag = false"
            >
              <template #item="{ element, index }">
                <div
                  class="flex items-center justify-between p-3 border rounded-lg cursor-move hover:shadow-md transition-shadow"
                >
                  <div class="flex items-center space-x-3">
                    <div class="text-gray-400">
                      <el-icon><rank /></el-icon>
                    </div>
                    <div class="flex-1">
                      <div class="font-medium text-sm">{{ element.name }}</div>
                      <div class="text-xs text-gray-500">
                        大小: {{ formatFileSize(element.size) }}
                        <span v-if="element.duration">
                          | 时长: {{ formatDuration(element.duration) }}
                        </span>
                        | 格式: {{ getFileExtension(element.name) }}
                      </div>
                    </div>
                  </div>
                  <div class="flex items-center space-x-2">
                    <span class="text-xs text-gray-400">序号: {{ index + 1 }}</span>
                    <el-button
                      type="danger"
                      size="small"
                      text
                      @click="removeFile(index)"
                    >
                      删除
                    </el-button>
                  </div>
                </div>
              </template>
            </draggable>
          </div>
        </div>
      </el-form-item>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleCancel">取消</el-button>
        <el-button
          type="primary"
          @click="handleSubmit"
          :loading="uploading"
          :disabled="mediaFileList.length === 0"
        >
          {{ uploading ? '上传中...' : '确认上传' }}
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { UploadFilled, Rank } from '@element-plus/icons-vue'
import type { FormInstance, FormRules, UploadFile } from 'element-plus'
import draggable from 'vuedraggable'
import meetingApi, { type CreateMeetingRequest, type UploadToken } from '@/api/meetingApi'

interface MediaFileItem extends UploadFile {
  duration?: number
}

// Props 和 Events
interface Props {
  modelValue: boolean
}

interface Emits {
  (e: 'update:modelValue', value: boolean): void
  (e: 'success', data: CreateMeetingRequest): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

// 响应式数据
const formRef = ref<FormInstance>()
const uploadRef = ref()
const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

const form = reactive<Omit<CreateMeetingRequest, 'audioOriginUrls' | 'audioState'>>({
  name: '',
  meetType: '',
  users: '',
  desc: '',
  audioOriginSize: 0,
  audioDuration: 0,
  audioUrl: '',
  audioSize: 0
})

const mediaFileList = ref<MediaFileItem[]>([])
const uploading = ref(false)
const drag = ref(false)

// 表单验证规则
const rules: FormRules = {
  name: [
    { required: true, message: '请输入会议名称', trigger: 'blur' },
    { min: 2, max: 50, message: '会议名称长度在 2 到 50 个字符', trigger: 'blur' }
  ],
  audioFiles: [
    { 
      validator: (rule, value, callback) => {
        if (mediaFileList.value.length === 0) {
          callback(new Error('请选择至少一个音视频文件'))
        } else {
          callback()
        }
      },
      trigger: 'change'
    }
  ]
}

// 获取文件扩展名
const getFileExtension = (fileName: string): string => {
  return fileName.split('.').pop()?.toLowerCase() || ''
}

// 检查文件类型是否为音视频
const isMediaFile = (file: File): boolean => {
  return file.type.startsWith('audio/') || file.type.startsWith('video/')
}

// 检查多文件后缀是否一致
const checkFileExtensionConsistency = (newFile: UploadFile): boolean => {
  if (mediaFileList.value.length === 0) return true
  
  const newExtension = getFileExtension(newFile.name)
  const existingExtension = getFileExtension(mediaFileList.value[0].name)
  
  return newExtension === existingExtension
}

// 获取文件类型文本
const getFileTypeText = (): string => {
  if (mediaFileList.value.length === 0) return '音视频'
  
  const firstFile = mediaFileList.value[0]
  if (firstFile.raw?.type.startsWith('audio/')) {
    return '音频'
  } else if (firstFile.raw?.type.startsWith('video/')) {
    return '视频'
  }
  return '音视频'
}

// 处理文件选择
const handleFileChange = (file: UploadFile, fileList: UploadFile[]) => {
  // 检查是否为音视频文件
  if (!file.raw || !isMediaFile(file.raw)) {
    ElMessage.error('只能上传音频或视频文件')
    return
  }

  // 检查文件扩展名一致性
  if (!checkFileExtensionConsistency(file)) {
    const existingExtension = getFileExtension(mediaFileList.value[0].name)
    const newExtension = getFileExtension(file.name)
    ElMessage.error(`文件格式不一致！已选择 .${existingExtension} 格式，不能添加 .${newExtension} 格式文件`)
    return
  }

  // 检查文件大小（限制500MB）
  if (file.size && file.size > 500 * 1024 * 1024) {
    ElMessage.error('文件大小不能超过500MB')
    return
  }

  // 检查是否已存在同名文件
  const existingIndex = mediaFileList.value.findIndex(f => f.name === file.name)
  if (existingIndex !== -1) {
    ElMessage.warning('该文件已存在')
    return
  }

  // 添加媒体时长信息
  if (file.raw) {
    const isVideo = file.raw.type.startsWith('video/')
    const element = isVideo ? document.createElement('video') : document.createElement('audio')
    const url = URL.createObjectURL(file.raw)
    element.src = url
    
    element.addEventListener('loadedmetadata', () => {
      const fileWithDuration: MediaFileItem = {
        ...file,
        duration: Math.round(element.duration)
      }
      
      mediaFileList.value.push(fileWithDuration)
      URL.revokeObjectURL(url)
    })

    element.addEventListener('error', () => {
      const fileWithDuration: MediaFileItem = {
        ...file,
        duration: 0
      }
      
      mediaFileList.value.push(fileWithDuration)
      URL.revokeObjectURL(url)
    })
  }
}

// 移除文件
const removeFile = (index: number) => {
  mediaFileList.value.splice(index, 1)
}

// 格式化文件大小
const formatFileSize = (size: number) => {
  if (size < 1024) return size + ' B'
  if (size < 1024 * 1024) return (size / 1024).toFixed(2) + ' KB'
  if (size < 1024 * 1024 * 1024) return (size / (1024 * 1024)).toFixed(2) + ' MB'
  return (size / (1024 * 1024 * 1024)).toFixed(2) + ' GB'
}

// 格式化时长
const formatDuration = (duration: number) => {
  const hours = Math.floor(duration / 3600)
  const minutes = Math.floor((duration % 3600) / 60)
  const seconds = duration % 60
  
  if (hours > 0) {
    return `${hours}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`
  }
  return `${minutes}:${seconds.toString().padStart(2, '0')}`
}

// 处理提交
const handleSubmit = async () => {
  if (!formRef.value) return

  try {
    await formRef.value.validate()
    
    if (mediaFileList.value.length === 0) {
      ElMessage.error('请选择至少一个音视频文件')
      return
    }

    uploading.value = true

    // 获取上传凭证
    const token = await meetingApi.getUploadToken()

    // 逐一上传文件
    const uploadedUrls: string[] = []
    let totalSize = 0
    let totalDuration = 0

    for (const file of mediaFileList.value) {
      if (file.raw) {
        const url = await meetingApi.uploadFileToOSS(file.raw, token)
        uploadedUrls.push(url)
        totalSize += file.size || 0
        totalDuration += file.duration || 0
      }
    }

    // 构造请求数据
    const meetingData: CreateMeetingRequest = {
      name: form.name,
      meetType: form.meetType || undefined,
      users: form.users || undefined,
      desc: form.desc || undefined,
      audioOriginUrls: JSON.stringify(uploadedUrls),
      audioOriginSize: totalSize,
      audioDuration: totalDuration,
      audioState: 'uploaded'
    }

    // 调用创建会议接口
    await meetingApi.createMeeting(meetingData)

    emit('success', meetingData)
    ElMessage.success('上传成功')
    handleCancel()
  } catch (error: any) {
    let msg = error.message
    if (error.name && Array.isArray(error.name)) {
      msg = error.name[0]?.message
    }
    console.error('上传失败:', error)
    ElMessage.error( msg || '上传失败')
  } finally {
    uploading.value = false
  }
}

// 处理取消
const handleCancel = () => {
  // 重置表单
  Object.assign(form, {
    name: '',
    meetType: '',
    users: '',
    desc: '',
    audioOriginSize: 0,
    audioDuration: 0,
    audioUrl: '',
    audioSize: 0
  })
  
  mediaFileList.value = []
  
  if (formRef.value) {
    formRef.value.clearValidate()
  }
  
  visible.value = false
}
</script>

<style scoped>
.upload-demo {
  width: 100%;
}

.el-upload-dragger {
  width: 100%;
}

.sortable-ghost {
  opacity: 0.5;
}

.sortable-chosen {
  opacity: 0.8;
}
</style> 