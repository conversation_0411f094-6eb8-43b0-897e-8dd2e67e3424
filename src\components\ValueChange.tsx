// 值变化组件
import { ref, watch, computed, onMounted, defineComponent } from 'vue'

export  default defineComponent({
    props: {
        current: Number,
        previous: Number,
        isPercentage: <PERSON><PERSON>an,
        isCurrency: <PERSON><PERSON><PERSON>,
        inverse: <PERSON><PERSON><PERSON>
    },
    setup(props) {
        const diff = computed(() => {
            if (props.current === undefined || props.previous === undefined) return null;
            return props.current - props.previous;
        });

        const formattedDiff = computed(() => {
            if (diff.value === null) return '';

            // 如果差异太小，不显示
            if (Math.abs(diff.value) < 0.0001) return '';

            const prefix = diff.value > 0 ?  '↑ ' : '↓ ';
            
            if (props.isPercentage) {
                // 对于百分比，显示百分点变化
                const percentChange = (diff.value * 100).toFixed(1);
                return `${prefix}${percentChange}%`;
            } else if (props.isCurrency) {
                // 对于货币，显示整数变化
                return `${prefix}¥${Math.abs(diff.value).toLocaleString('zh-CN')}`;
            } else {
                // 默认显示
                return `${prefix}${diff.value.toFixed(2)}`;
            }
        });

        const changeClass = computed(() => {
            if (diff.value === null) return '';

            const isPositive = props.inverse ? diff.value < 0 : diff.value > 0;
            const isNegative = props.inverse ? diff.value > 0 : diff.value < 0;

            if (isPositive) {
                return 'ml-2 text-xs text-green-600';
            } else if (isNegative) {
                return 'ml-2 text-xs text-red-600';
            }
            return '';
        });

        return () => {
            if (!formattedDiff.value) return null;

            return (
                <span class= { changeClass.value } >
                { formattedDiff.value }
                </span>
            );
        };
    }
});
