import './assets/main.css'

import { createApp } from 'vue'
import { createPinia } from 'pinia'
import ElementPlus from 'element-plus'
import 'element-plus/dist/index.css'

import { renderWithQiankun, qiankunWindow } from "vite-plugin-qiankun/es/helper";
import App from './App.vue'
import router from './router'

var root:any = null;
function render(props: any) {
    const { container } = props;

    if (qiankunWindow.__POWERED_BY_QIANKUN__) {
        const styleNode = document.createElement('style');
        styleNode.textContent = `
            html[data-qiankun-app="dataAnalysis"] {
                --data-analysis-prefix: true;
            }
        `;
        document.head.appendChild(styleNode);
        //alert("添加样式前缀");
        
        document.documentElement.setAttribute('data-qiankun-app', 'dataAnalysis');
    }

    root = createApp(App)
    root.use(ElementPlus)
    root.use(create<PERSON>inia())
    root.use(router)

    const c = container ? container.querySelector("#app") : document.getElementById("app")
    root.mount(c)
}

renderWithQiankun({
    mount(props) {
        render(props);
    },
    bootstrap() {
        console.log("board app bootstraped");
    },
    update() {
        console.log("board app updated");
    },
    unmount(props) {
        console.log("board unmount");
        root && root.unmount();
        
        if (qiankunWindow.__POWERED_BY_QIANKUN__) {
            document.documentElement.removeAttribute('data-qiankun-app');
        }
    }
});

if (!qiankunWindow.__POWERED_BY_QIANKUN__) {
    // 独立开发环境下也需要添加样式隔离属性
    document.documentElement.setAttribute('data-qiankun-app', 'dataAnalysis');
    
    const styleNode = document.createElement('style');
    styleNode.textContent = `
        /* 添加一个全局的样式前缀作用域 */
        html[data-qiankun-app="dataAnalysis"] {
            --data-analysis-prefix: true;
        }
    `;
    document.head.appendChild(styleNode);
    
    render({});
}