import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { caseApi } from '@/api/caseApi'

// 案例申请接口类型定义
export interface CaseApplication {
  _id: string
  name: string
  imageUrl: string
  description: string
  contact: string
  phone: string
  isPublic: boolean
  attachments: string[]
  status: 'pending' | 'approved' | 'rejected'
  submitTime: string
  userId?: string
  createdAt?: string
  updatedAt?: string
}

// 案例申请表单类型
export interface CaseForm {
  name: string
  imageUrl: string
  description: string
  contact: string
  phone: string
  isPublic: boolean
  attachments: string[]
}

export const useCaseStore = defineStore('case', () => {
  // 状态
  const caseApplications = ref<CaseApplication[]>([])
  const loading = ref(false)
  const error = ref<string | null>(null)

  // 计算属性 - 统计数据
  const statistics = computed(() => {
    const total = caseApplications.value.length
    const pending = caseApplications.value.filter(item => item.status === 'pending').length
    const approved = caseApplications.value.filter(item => item.status === 'approved').length
    const rejected = caseApplications.value.filter(item => item.status === 'rejected').length
    
    return {
      total,
      pending,
      approved,
      rejected
    }
  })

  // 获取案例列表
  const fetchCases = async () => {
    loading.value = true
    error.value = null
    
    try {
      const result:any = await caseApi.getCases()
      caseApplications.value = result || [];
    } catch (err) {
      console.error('获取案例列表失败:', err)
      // 如果API失败，使用模拟数据
      loadMockData()
    } finally {
      loading.value = false
    }
  }

  // 创建案例申请
  const createCase = async (caseData: CaseForm): Promise<CaseApplication> => {
    loading.value = true
    error.value = null

    try {
      const newCase = await caseApi.createCase(caseData)
      caseApplications.value.unshift(newCase)
      return newCase
    } catch (err) {
      console.error('创建案例失败:', err)
      // 模拟创建
      const mockCase: CaseApplication = {
        _id: Date.now().toString(),
        ...caseData,
        status: 'pending',
        submitTime: new Date().toISOString().split('T')[0],
        userId: 'current-user-id'
      }
      caseApplications.value.unshift(mockCase)
      return mockCase
    } finally {
      loading.value = false
    }
  }

  // 更新案例状态
  const updateCaseStatus = async (caseId: string, status: CaseApplication['status']) => {
    loading.value = true
    error.value = null

    try {
      await caseApi.updateCaseStatus(caseId, status)

      // 更新本地状态
      const caseIndex = caseApplications.value.findIndex(item => item._id === caseId)
      if (caseIndex !== -1) {
        caseApplications.value[caseIndex].status = status
        caseApplications.value[caseIndex].updatedAt = new Date().toISOString()
      }
    } catch (err) {
      console.error('更新案例状态失败:', err)
      // 模拟更新
      const caseIndex = caseApplications.value.findIndex(item => item._id === caseId)
      if (caseIndex !== -1) {
        caseApplications.value[caseIndex].status = status
      }
    } finally {
      loading.value = false
    }
  }

  // 更新案例信息
  const updateCase = async (caseId: string, caseData: Partial<CaseForm>): Promise<CaseApplication> => {
    loading.value = true
    error.value = null

    try {
      const updatedCase = await caseApi.updateCase(caseId, caseData)

      // 更新本地状态
      const caseIndex = caseApplications.value.findIndex(item => item._id === caseId)
      if (caseIndex !== -1) {
        caseApplications.value[caseIndex] = updatedCase
      }

      return updatedCase
    } catch (err) {
      console.error('更新案例失败:', err)
      // 模拟更新
      const caseIndex = caseApplications.value.findIndex(item => item._id === caseId)
      if (caseIndex !== -1) {
        Object.assign(caseApplications.value[caseIndex], caseData)
        caseApplications.value[caseIndex].updatedAt = new Date().toISOString()
        return caseApplications.value[caseIndex]
      }
      throw err
    } finally {
      loading.value = false
    }
  }

  // 删除案例
  const deleteCase = async (caseId: string) => {
    loading.value = true
    error.value = null

    try {
      await caseApi.deleteCase(caseId)

      // 从本地状态中移除
      const caseIndex = caseApplications.value.findIndex(item => item._id === caseId)
      if (caseIndex !== -1) {
        caseApplications.value.splice(caseIndex, 1)
      }
    } catch (err) {
      console.error('删除案例失败:', err)
      // 模拟删除
      const caseIndex = caseApplications.value.findIndex(item => item._id === caseId)
      if (caseIndex !== -1) {
        caseApplications.value.splice(caseIndex, 1)
      }
    } finally {
      loading.value = false
    }
  }

  // 上传附件
  const uploadAttachment = async (file: File): Promise<string> => {
    try {
      const result = await caseApi.uploadAttachment(file)
      return result.url
    } catch (err) {
      console.error('文件上传失败:', err)
      // 模拟上传成功
      return `https://example.com/attachments/${file.name}`
    }
  }

  // 下载附件
  const downloadAttachment = async (attachmentUrl: string, fileName: string) => {
    try {
      const link = document.createElement('a')
      link.href = attachmentUrl
      link.download = fileName
      link.target = '_blank'
      
      if (attachmentUrl.startsWith('http')) {
        link.click()
        return true
      } else {
        // 模拟下载
        console.log(`下载文件: ${fileName}`)
        return true
      }
    } catch (err) {
      console.error('下载文件失败:', err)
      return false
    }
  }

  // 加载模拟数据
  const loadMockData = () => {
    caseApplications.value = [
      {
        _id: '507f1f77bcf86cd799439011',
        name: '智能客服系统解决方案',
        imageUrl: 'https://images.unsplash.com/photo-1551434678-e076c223a692?w=400',
        description: '基于AI技术的智能客服系统，提供24小时自动化客户服务支持，包含自然语言处理、情感分析、智能路由等核心功能模块',
        contact: '张三',
        phone: '13800138001',
        isPublic: true,
        attachments: ['智能客服系统方案详情.pdf', '技术架构图.png', '产品演示视频.mp4'],
        status: 'pending',
        submitTime: '2024-01-15',
        userId: 'user1',
        createdAt: '2024-01-15T10:00:00Z',
        updatedAt: '2024-01-15T10:00:00Z'
      },
      {
        _id: '507f1f77bcf86cd799439012',
        name: '电商平台优化方案',
        imageUrl: 'https://images.unsplash.com/photo-1556742049-0cfed4f6a45d?w=400',
        description: '针对电商平台的性能优化和用户体验提升的综合解决方案，包含页面加载优化、搜索算法改进、推荐系统升级等',
        contact: '李四',
        phone: '13800138002',
        isPublic: false,
        attachments: ['电商平台优化报告.docx', '性能测试数据.xlsx'],
        status: 'approved',
        submitTime: '2024-01-10',
        userId: 'user2',
        createdAt: '2024-01-10T09:00:00Z',
        updatedAt: '2024-01-12T14:30:00Z'
      },
      {
        _id: '507f1f77bcf86cd799439013',
        name: '移动应用设计方案',
        imageUrl: 'https://images.unsplash.com/photo-1512941937669-90a1b58e7e9c?w=400',
        description: '现代化移动应用UI/UX设计方案，注重用户体验和视觉效果，采用Material Design设计语言',
        contact: '王五',
        phone: '13800138003',
        isPublic: true,
        attachments: ['移动应用设计稿.sketch', '交互原型.fig', '设计规范文档.pdf'],
        status: 'rejected',
        submitTime: '2024-01-05',
        userId: 'user3',
        createdAt: '2024-01-05T16:20:00Z',
        updatedAt: '2024-01-08T11:15:00Z'
      }
    ]
  }

  // 清除错误状态
  const clearError = () => {
    error.value = null
  }

  // 初始化数据
  const init = async () => {
    await fetchCases()
  }

  return {
    // 状态
    caseApplications,
    loading,
    error,
    statistics,
    
    // 方法
    fetchCases,
    createCase,
    updateCase,
    updateCaseStatus,
    deleteCase,
    uploadAttachment,
    downloadAttachment,
    loadMockData,
    clearError,
    init
  }
}) 