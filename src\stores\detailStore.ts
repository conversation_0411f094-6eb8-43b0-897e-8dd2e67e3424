import { defineStore } from 'pinia'
import { ref, computed, watch} from 'vue'
import { useRoomList } from './roomList'
import { host } from '@/comm/config'
import { useNatsStore } from './natsStore'

// 模块配置类型
interface ModuleConfig {
  id: string
  title: string
}

// 千川数据类型
interface QianchuanData {
  label: string
  value: string
  trend: string
  status: 'up' | 'down'
}

//房间概览数据
interface RoomCoreData {
  roomPayAmt: number                    // 成交金额
  roomGpm: number                       // 千次观看成交金额
  roomPayUcnt: number                   // 成交人数
  roomPayComboCnt: number              // 成交件数
  roomWatchPayUcntRatio: number        // 观看-成交率(人数)
  roomProductClickPayUcntRatio: number // 商品点击-成交率(人数)
  roomOnlineUserCnt: number            // 实时在线人数
  roomLiveShowWatchCntRatio: number    // 曝光-观看率(次数)
  roomAvgWatchDuration: number         // 人均观看时长
  roomWatchInteractUcntRatio: number   // 观看-互动率(人数)
  roomFollowAnchorUcnt: number         // 新增粉丝数
}


//房间概览数据
interface RoomProfile {
  _id:string
  userName: string
  userAvator: string
  startAt: string
  endAt: string
  status: number
  roomId: string
  qianChuan: QianchuanData
  roomCoreData: RoomCoreData
  goods: Goods[]
}


// 商品数据类型 - 根据后端定义
interface Goods {
  id: string
  name: string
  thumb: string
  goodsExposeToClickRate: number    // 商品曝光点击率
  goodsExposeToBuyRate: number      // 商品曝光转化率
  goodsClickToBuyRate: number       // 商品点击转化率
  goodsTotalTurnover: number        // 商品总成交额
  goodsRefundRate: number           // 商品退款率
}

export const useDetailStore = defineStore('detail', () => {

  const productViewType = ref('list')
  // 排序面板显示状态
  const showSortPanel = ref(false)
  const showSortPanelId = ref('')


  // 模块配置
  const modules = ref<ModuleConfig[]>([
    { id: 'overview', title: '直播间概览' },
    { id: 'products', title: '商品信息' },
    { id: 'audience', title: '人群画像' },
    { id: 'traffic', title: '流量分析' },
    { id: 'qianchuan', title: '千川数据看板' }
  ])



  // 千川数据
  const qianchuanData = ref<QianchuanData[]>([
    { label: '总投放预算', value: '¥50,000', trend: '+15.2%', status: 'up' },
    { label: '已消耗预算', value: '¥32,456', trend: '+12.8%', status: 'up' },
    { label: '点击转化率', value: '4.5%', trend: '+0.8%', status: 'up' },
    { label: '展现量', value: '89,234', trend: '+21.5%', status: 'up' },
    { label: '点击量', value: '12,345', trend: '+18.3%', status: 'up' },
    { label: '平均点击成本', value: '¥2.63', trend: '-5.2%', status: 'down' },
    { label: '千次展现成本', value: '¥363.73', trend: '-3.8%', status: 'down' },
    { label: '投放ROI', value: '3.21', trend: '+8.9%', status: 'up' },
    { label: '新增粉丝数', value: '2,345', trend: '+25.6%', status: 'up' }
  ])

  // Actions
  function toggleSortPanel(moduleId: string = '') {
    if (moduleId) {
      showSortPanelId.value = showSortPanelId.value === moduleId ? '' : moduleId
    } else {
      showSortPanel.value = !showSortPanel.value
    }
  }

  function handleSortEnd() {
    showSortPanel.value = false
    showSortPanelId.value = ''
  }

  const roomDetail = ref<RoomProfile>({goods:[]}as any)
  const liveList = useRoomList()
  const nats = useNatsStore()
  
  const loading = ref(false)
  
  // 添加获取房间详情的方法
  async function fetchRoomDetail(roomId: string) {
    if (!roomId) return
    
    loading.value = true
    try {
      const response = await fetch(`${host}/room/detail/${roomId}`)
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }
      const result = await response.json()
      if (result.code === 200) {
        roomDetail.value = result.data
      }
    } catch (error) {
      console.error('Error fetching room detail:', error)
    } finally {
      loading.value = false
    }
  }

    // 添加获取房间详情的方法
    async function fetchRoomGoods(roomId: string) {
      if (!roomId) return
      
      loading.value = true
      try {
        const response = await fetch(`${host}/room/goods/${roomId}`)
        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`)
        }
        const result = await response.json()
        if (result.code === 200) {
          roomDetail.value.goods = result.data
        }
      } catch (error) {
        console.error('Error fetching room detail:', error)
      } finally {
        loading.value = false
      }
    }

      // 添加获取房间详情的方法
      async function fetchRoomCore(roomId: string) {
        if (!roomId) return
        
        loading.value = true
        try {
          const response = await fetch(`${host}/room/coredata/${roomId}`)
          if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`)
          }
          const result = await response.json()
          if (result.code === 200) {
            roomDetail.value.roomCoreData = result.data
          }
        } catch (error) {
          console.error('Error fetching room detail:', error)
        } finally {
          loading.value = false
        }
      }

   // 监听 currSelId 的变化
   watch(
    () => liveList.currSelId,
    (newId) => {
      if (newId) {
        fetchRoomDetail(newId)
      } else {
        roomDetail.value = {goods:[]} as any;
      }
    },
    { immediate: true }
  )

  // 监听列表与图表的切换和实时数据的更新
  watch(
    [
      productViewType,
      ()=>nats.goodDirty
    ],   
    () => {
      if (liveList.currSelId && productViewType.value == 'list') {
        fetchRoomGoods(liveList.currSelId)
      }
    }
  )

  watch(
    ()=>nats.roomCoreDirty,    
    () => {
      fetchRoomCore(liveList.currSelId)
    }
  )

  
  // 计算概览数据
const overviewStats = computed(() => {
  const data = roomDetail.value.roomCoreData as RoomCoreData
  if (!data) return []

  return [
      { label: '成交金额', value: data.roomPayAmt, type: 'money' },
      { label: '千次观看成交金额', value: data.roomGpm, type: 'money' },
      { label: '成交人数', value: data.roomPayUcnt, type: 'number' },
      { label: '成交件数', value: data.roomPayComboCnt, type: 'number' },
      { label: '观看-成交率', value: data.roomWatchPayUcntRatio, type: 'percent' },
      { label: '商品点击-成交率', value: data.roomProductClickPayUcntRatio, type: 'percent' },
      { label: '实时在线人数', value: data.roomOnlineUserCnt, type: 'number' },
      { label: '曝光-观看率', value: data.roomLiveShowWatchCntRatio, type: 'percent' },
      { label: '人均观看时长', value: data.roomAvgWatchDuration, type: 'duration' },
      { label: '观看-互动率', value: data.roomWatchInteractUcntRatio, type: 'percent' },
      { label: '新增粉丝数', value: data.roomFollowAnchorUcnt, type: 'number' }
  ]
})

const productData = computed(() => {
  return roomDetail.value.goods || [] as Goods[]
})


  return {
    loading,
    overviewStats,
    productViewType,
    roomDetail,

    modules,
    showSortPanel,
    showSortPanelId,
    productData,
    qianchuanData,
    toggleSortPanel,
    handleSortEnd
  }
})