// import { fetchGoodsHistory } from '@/comm/http';
import { defineStore } from 'pinia'
import { computed, ref, watch} from 'vue'
import { useDetailStore } from '@/stores/detailStore';
import { useNatsStore } from '@/stores/natsStore';

import { GoodsDimensions } from '@/comm/const';
//@ts-ignore
import { debounce } from 'lodash';


export const useHistoryStore = defineStore('history', () => {

    const detail = useDetailStore()
    const nats = useNatsStore()

    const selectedDimension = ref(GoodsDimensions[0].value);
    const selectedProducts = ref<string[]>([]);
    const currGoodsHistory = ref<{ id: string, value: number, time: string }[]>([]);
    const version =  ref(0);


    async function fetchGoodsHis(sessionId: string, ids: string[], dimKey:string) {
        if (!sessionId) return

        //根据当前的维度从服务器获取历史数据    
        // const ret = await fetchGoodsHistory(sessionId, ids, dimKey)
        // if (ret.code != 200) return;
        // currGoodsHistory.value = ret.data as any[]  || []

        version.value++;
    }

    const xAxisData = computed(()=>{
        const timeData = Array.from(new Set(currGoodsHistory.value.map(item => item.time)))
        .sort()
        .map(time => {
            // 假设time格式为 "YYYY-MM-DD HH:mm:ss"
            // 只显示时间部分 "HH:mm"
            const date = new Date(time + "Z");
            return date.toLocaleTimeString([], {
                hour: '2-digit',
                minute: '2-digit',
                hour12: false
            });
        });
        return timeData
    });


   // 生成模拟的商品数据
   const generateProductData = (productId: string) => {
       return currGoodsHistory.value.filter(item => item.id == productId).map(item => item.value)
   };

    const series =  computed(()=>{
        return selectedProducts.value.map(productId => {
            const product = detail.productData.find(p => p.id === productId);
            return {
                name: product?.name,
                type: 'line',
                smooth: true,
                data: generateProductData(productId),
                symbol: 'circle',
                symbolSize: 8,
                emphasis: {
                    focus: 'series'
                }
            };
        });
    });
    const debouncedFetch = debounce(() => {
        if (detail.productViewType != "chart") return;
        fetchGoodsHis(detail.roomDetail._id, selectedProducts.value, selectedDimension.value)
    }, 200);

    //监听
    // 监听数据变化
    watch([()=>selectedDimension,  ()=>selectedProducts,()=>detail.productViewType, () => nats.goodDirty, () => detail.productData], () => {
        debouncedFetch();
    }, { deep: true, immediate: true });
    
    // 监听原始数据变化
    watch(() => detail.productData, (newData) => {
        // 如果还没有选择商品，默认选择前5个
        if (selectedProducts.value.length === 0 && newData?.length) {
            selectedProducts.value = newData.slice(0, 5).map(p => p.id);
        }
    }, { immediate: true });


   return {
        series,
        xAxisData,
        version,
        selectedDimension,
        selectedProducts,
    }
})


 