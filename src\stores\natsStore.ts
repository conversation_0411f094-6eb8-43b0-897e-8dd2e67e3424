import { host, natsurl, parentId } from "@/comm/config";
import { defineStore } from 'pinia'
import { ref, watch, type Ref } from 'vue'

// import { Empty, StringCodec, connect } from "nats.ws/lib/src/mod.js";
//@ts-ignore
import { Empty, StringCodec, connect } from "nats.ws";
import { useRoomList } from './roomList'


async function subscribe(conn:any, subject: string, callback: any) {
  if (!conn) {
    console.error("nats connic not connected");
    return;
  }
  const sc = StringCodec();
  const sub = conn.subscribe(subject);
  (async () => {
    for await (const m of sub) {
      const ret = sc.decode(m.data);
      console.log(subject, "=>recieved",ret);
      try {
        if ((ret && ret[0] == "[") || ret[0] == "{") {
          const msg = JSON.parse(ret);
          callback(msg);
        } else {
          callback(ret);
        }
      } catch (error) {
        console.log(subject, "=>recieved json parse eror", ret);
        console.log(error);
      }
      console.log(subject, "=>recieved over");
    }
    console.log(subject, "subscription closed");
  })();
  return function () {
    sub.unsubscribe();
  };
}

function SubTopic(conn:any,  topic: string, dirty:  Ref<number>, cbref:  Ref<any>) {
  if (cbref.value) {
    cbref.value();
  }
  subscribe(conn, topic, (msg: any) => {
    dirty.value++;
  }).then(cb=>{
    cbref.value = cb;
  });
}

export const useNatsStore = defineStore('nats', () => {
  
  const conn = ref<any>(null);

  const goodDirty = ref(0);
  const liveList = useRoomList();
  const goodsUpdateRef = ref<any>(null);
  const roomCoreUpdateRef = ref<any>(null);
  const roomCoreDirty = ref(0);


  watch(
    () => liveList.currSelId,
    async (newId) => {
      if (newId) {
        if (!conn.value) {
          const c = await connect({ servers: natsurl });
          conn.value = c;
          console.log("nats connected");
        } 
        SubTopic(conn.value,  "goods.updated." + newId, goodDirty, goodsUpdateRef);
        SubTopic(conn.value,  "roomcore.updated." + newId, roomCoreDirty, roomCoreUpdateRef);
        return
      }

      if (goodsUpdateRef.value) {
        goodsUpdateRef.value();
      }
      if (roomCoreUpdateRef.value) {
        roomCoreUpdateRef.value();
      }
    },
    { immediate: true }
  )
  
  return {
    goodDirty,
    roomCoreDirty,
  }
})


