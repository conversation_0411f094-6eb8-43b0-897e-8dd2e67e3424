import { host, parentId } from "@/comm/config";
import { defineStore } from 'pinia'

interface RoomMeta {
    _id: string;
    userName: string;
    userAvator: string;
    userId: string;
    status: number;
    startAt: string;
    endAt?: string;
    roomCoreData: {
        roomPayAmt: number;
    }
    roomId: string;
}

export const useRoomList = defineStore("roomList", {
    state: () => ({
      size: 10,
      page: 1,
      total: 0,
      list:  [] as RoomMeta[],
      currSelId: "",
      currUserId: "",
      loading: false,
    }),
    getters: {
        duration: (state) => {
            return "duration"
        },

      currItem: (state) => {
        if (!state.currSelId) return null;
        let n = state.list.length;
        while (n--) {
          if (state.list[n]._id == state.currSelId) return state.list[n];
        }
        return null;
      },
      canLoadNext: (state) => {
        return state.page * state.size < state.total;
      },
    },

    actions: {
      async resumeShow() {
        if (this.list.length < 1) {
          await this.loadPage(1);
        }
      },
      //刷新页面，重新获取数据
      async fresh() {
        await this.loadPage(this.page, this.size);
      },
      loadNextPage(){
        return this.loadPage(this.page + 1);
      },

      async loadPage(page: number, size?: number): Promise<boolean> {
    
        this.loading = true;
        if (!size) size = this.size;
    
          // 构建带查询参数的URL
        const apiUrl = new URL(`${host}/room/list?parentId=${parentId}`);
        apiUrl.searchParams.append('page', page+"");
        apiUrl.searchParams.append('size', size+"");

        if (this.currUserId) {
          apiUrl.searchParams.append('userId', this.currUserId);
        }
       
        let ret = {} as any;
        try {
          const response = await fetch(apiUrl.toString());
          if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
          }
          ret = await response.json();
        } catch (error) {
            this.loading = false;
          console.error('Error fetching paged data:', error);
          throw error;
        }
        this.loading = false;

        if (ret.code != 200) {
            return false;
        }

        const result = ret.data;
        this.page = page;
        this.size = size;
        let list = result.list;
          if (page == 1) {
            this.list = list;
          } else {
            this.list = [...this.list, ...list];
          }
         
          this.total = result.total;

          console.log(this.list);

          return true;  
      }
    }
  })
