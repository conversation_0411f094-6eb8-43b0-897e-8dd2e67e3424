import { host, parentId } from "@/comm/config";
import { defineStore } from 'pinia'

interface UserMeta {
    userId: string;
    userName: string;
    userAvator: string;
}

export const useUserList = defineStore("userList", {
    state: () => ({
      list:  [] as UserMeta[],
    }),
    actions: {
      async load(key: string): Promise<boolean> {
          // 构建带查询参数的URL
        const apiUrl = new URL(`${host}/user/list?parentId=${parentId}`);
        if (key) apiUrl.searchParams.append('key', key);
        let ret = {} as any;
        try {
          const response = await fetch(apiUrl.toString());
          if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
          }
          ret = await response.json();
        } catch (error) {        
          console.error('Error fetching user list data:', error);
          return false;
        }
        if (ret.code != 200) {
            return false;
        }
        const result = ret.data;
        this.list =  result.list;
          return true;  
      }
    }
  })
