import { defineStore } from 'pinia'
import { ref } from 'vue'
import axios from 'axios'

interface LoginCredentials {
  account: string
  password: string
}

interface LoginResponse {
  code: number
  msg: string
  data: {
    token: string
    user: {
      id: number
      account: string
      name: string | null
      email: string | null
      id_card_number: string | null
      nickname: string
      avatar: string | null
      invitation_code: string
      expiration_time: string
      last_login_time: string
      created_at: string
      updated_at: string
      last_login: string | null
      role: number
      password: string
    }
    is_show_exit_strategy: boolean
  }
}

export interface UserInfo {
  id?: number
  account?: string
  name?: string
  nickname?: string
  avatar?: string
  email?: string
  role?: number
  expiration_time?: string
}

export interface SubAccount {
  id: string
  name: string
  avatar: string
  email: string
  phone: string
  role: 'admin' | 'editor' | 'viewer'
  joinTime: string
}

export const useUserStore = defineStore('user', () => {
  const isLoggedIn = ref(false)
  const userInfo = ref<UserInfo | null>(null)
  const token = ref<string>('')
  const subAccounts = ref<SubAccount[]>([
    {
      id: '1',
      name: '张三',
      avatar: 'https://randomuser.me/api/portraits/men/2.jpg',
      email: '<EMAIL>',
      phone: '***********',
      role: 'editor',
      joinTime: '2023-05-15'
    },
    {
      id: '2',
      name: '李四',
      avatar: 'https://randomuser.me/api/portraits/women/2.jpg',
      email: '<EMAIL>',
      phone: '***********',
      role: 'viewer',
      joinTime: '2023-06-20'
    }
  ])
  
  const login = async (credentials: LoginCredentials): Promise<boolean> => {
    try {
      const response = await axios.post<LoginResponse>(
        'https://meeting.sensearray.com/usercenter/api/v1/login',
        {
          login_type: 'password',
          account: credentials.account,
          password: credentials.password
        }
      )

      if (response.data.code === 200) {
        const { token: userToken, user } = response.data.data
        
        // 保存登录状态
        isLoggedIn.value = true
        token.value = userToken
        userInfo.value = {
          id: user.id,
          account: user.account,
          name: user.name || undefined,
          nickname: user.nickname,
          avatar: user.avatar || undefined,
          email: user.email || undefined,
          role: user.role,
          expiration_time: user.expiration_time
        }

        // 可选：保存到 localStorage
        localStorage.setItem('token', userToken)
        localStorage.setItem('userInfo', JSON.stringify(userInfo.value))

        return true
      } else {
        throw new Error(response.data.msg || '登录失败')
      }
    } catch (error: any) {
      console.error('登录失败:', error)
      throw new Error(error.response?.data?.msg || error.message || '登录失败')
    }
  }

  const logout = () => {
    isLoggedIn.value = false
    userInfo.value = null
    token.value = ''
    
    // 清除本地存储
    localStorage.removeItem('token')
    localStorage.removeItem('userInfo')
  }

  // 初始化时检查本地存储
  const initFromStorage = () => {
    const savedToken = localStorage.getItem('token')
    const savedUserInfo = localStorage.getItem('userInfo')
    
    if (savedToken && savedUserInfo) {
      try {
        token.value = savedToken
        userInfo.value = JSON.parse(savedUserInfo)
        isLoggedIn.value = true
      } catch (error) {
        console.error('解析用户信息失败:', error)
        logout()
      }
    }
  }

  const addSubAccount = (account: SubAccount) => {
    subAccounts.value.push(account)
  }

  const removeSubAccount = (id: string) => {
    const index = subAccounts.value.findIndex(acc => acc.id === id)
    if (index !== -1) {
      subAccounts.value.splice(index, 1)
    }
  }

  const updateSubAccountRole = (id: string, role: 'admin' | 'editor' | 'viewer') => {
    const account = subAccounts.value.find(acc => acc.id === id)
    if (account) {
      account.role = role
    }
  }

  const updateUserInfo = (info: Partial<UserInfo>) => {
    if (userInfo.value) {
      userInfo.value = { ...userInfo.value, ...info }
      localStorage.setItem('userInfo', JSON.stringify(userInfo.value))
    }
  }

  return { 
    isLoggedIn, 
    userInfo, 
    token,
    subAccounts,
    login, 
    logout,
    initFromStorage,
    addSubAccount,
    removeSubAccount,
    updateSubAccountRole,
    updateUserInfo
  }
})
