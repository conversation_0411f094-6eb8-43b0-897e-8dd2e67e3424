<!-- 代码已包含 CSS：使用 TailwindCSS , 安装 TailwindCSS 后方可看到布局样式效果 -->
<template>
  <div class="flex bg-gray-100" style="height: calc(100vh - 64px);">
    <!-- 左侧直播列表 -->
    <RoomList></RoomList>
    <!-- 右侧详细信息 -->
    <div class="flex-1 h-full overflow-y-auto">
      <!-- 标题栏 - 添加 sticky 效果 -->
      <div class="sticky top-0 z-10 bg-gray-100 p-6 pb-0 pt-3">
        <div class="bg-white rounded-lg shadow-sm">
          <div class="p-4 flex items-center justify-between">
            <div class="flex items-center gap-4">
              <div class="flex items-center gap-3">
                <Avatar :avator-url="liveList.currItem?.userAvator" />
                <div class="flex flex-col">
                  <span class="font-medium text-lg">{{ liveList.currItem?.userName || '-' }}</span>
                  <div class="flex items-center gap-2 text-sm text-gray-500">
                    <span>开始：{{ formatTime(liveList.currItem?.startAt) }}</span>
                    <span class="text-gray-300">|</span>
                    <span>结束：{{ formatTime(liveList.currItem?.endAt) }}</span>
                    <span class="text-gray-300">|</span>
                    <span :class="[
                      'px-2 py-0.5 rounded text-xs',
                      liveList.currItem?.status === 1 ? 'bg-red-500 text-white' : 'bg-gray-500 text-white'
                    ]">
                      {{ liveList.currItem?.status === 1 ? '直播中' : '未开播' }}
                    </span>
                  </div>
                </div>
              </div>
            </div>
            <!-- 内容排序按钮 -->
            <div class="relative">
              <el-icon class="mr-1" @click="showSortPanel = !showSortPanel">
                <Operation />
              </el-icon>
              <SortPanel v-if="showSortPanel" v-model:modules="modules" @sort-end="showSortPanel = false" />
            </div>
          </div>
        </div>
      </div>


      <!-- 模块内容区域 - 添加上内边距 -->
      <div class="p-6">
        <!-- 原有的拖拽模块内容 -->
        <draggable v-model="modules" handle=".drag-handle" :item-key="'id'" :animation="300" class="space-y-6">
          <template #item="{ element }">
            <div class="bg-white rounded-lg shadow-sm">
              <div class="p-4 border-b border-gray-100 flex justify-between items-center relative">
                <h3 class="text-lg font-medium">{{ element.title }}</h3>

                <div class="flex items-center gap-4">
                  <el-radio-group v-model="detail.productViewType" class="!rounded-button"
                    v-if="element.id === 'products'">
                    <el-radio-button value="list">列表</el-radio-button>
                    <el-radio-button value="chart">图表</el-radio-button>
                  </el-radio-group>
                  <el-icon class="drag-handle cursor-move text-gray-400 sort-trigger"
                    @click="toggleSortPanel(element.id)">
                    <Operation />
                  </el-icon>
                </div>

                <SortPanel v-if="showSortPanelId === element.id" v-model:modules="modules" :curr-id="element.id"
                  @sort-end="showSortPanelId = ''" />

              </div>
              <!-- 直播间概览 -->
              <RoomOverView v-if="element.id === 'overview'" />

              <!-- 商品信息 -->
              <Goods v-if="element.id === 'products'" />

              <!-- 人群画像 -->
              <template v-if="element.id === 'audience'">
                <div class="p-6">
                  <div ref="audienceChartRef" style="height: 400px"></div>
                </div>
              </template>
              <!-- 流量分析 -->
              <template v-if="element.id === 'traffic'">
                <div class="p-6">
                  <div ref="trafficChartRef" style="height: 400px"></div>
                </div>
              </template>
              <!-- 千川数据 -->
              <template v-if="element.id === 'qianchuan'">
                <div class="p-6">
                  <div class="grid grid-cols-3 gap-4">
                    <div v-for="(item, index) in qianchuanData" :key="index"
                      class="p-4 rounded-lg bg-gray-50 hover:bg-gray-100 transition-colors">
                      <div class="text-sm text-gray-500 mb-1">{{ item.label }}</div>
                      <div class="flex items-center gap-2">
                        <div class="text-xl font-semibold">{{ item.value }}</div>
                        <div :class="[
                          'text-xs px-2 py-0.5 rounded flex items-center gap-1',
                          item.status === 'up' ? 'text-green-600 bg-green-50' : 'text-red-600 bg-red-50'
                        ]">
                          <el-icon class="text-[10px]">
                            <component :is="item.status === 'up' ? 'CaretTop' : 'CaretBottom'" />
                          </el-icon>
                          {{ item.trend }}
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </template>
            </div>
          </template>
        </draggable>
      </div>
    </div>
  </div>
</template>
<script lang="ts" setup>
import { ref, onMounted, computed, onUnmounted } from 'vue';
import { Search, Refresh, View, Operation, CaretTop, CaretBottom, Money } from '@element-plus/icons-vue';
import draggable from 'vuedraggable/src/vuedraggable'
import RoomList from "@/components/RoomList.vue"
import SortPanel from "@/components/SortPanel.vue"
import RoomOverView from '@/components/RoomOverView.vue';
import Goods from '@/components/Goods.vue';

import * as echarts from 'echarts';

import Avatar from '@/components/Avatar.vue'
import moment from 'moment';
import { useRoomList } from '@/stores/roomList';
import { useDetailStore } from '@/stores/detailStore';

const detail = useDetailStore();

// 替换原来的 showSortPanel
const showSortPanelId = ref('')

const toggleSortPanel = (moduleId: string) => {
  showSortPanelId.value = showSortPanelId.value === moduleId ? '' : moduleId
}

// 点击外部关闭排序面板
const handleClickOutside = (event: MouseEvent) => {
  const isToggleButton = (event.target as Element).closest('.sort-trigger');
  if (!isToggleButton && showSortPanelId.value && !(event.target as Element).closest('.sort-panel')) {
    showSortPanelId.value = ''
  }
}

onMounted(() => {
  document.addEventListener('click', handleClickOutside)
})
onUnmounted(() => {
  document.removeEventListener('click', handleClickOutside)
})

const liveList = useRoomList();


// 格式化时间
const formatTime = (time?: string) => {
  if (!time) return '-';
  return moment(time).format('YYYY-MM-DD HH:mm:ss');
}

const showSortPanel = ref(false);

// 模块配置
const modules = ref([
  { id: 'overview', title: '直播间概览' },
  { id: 'products', title: '商品信息' },
  { id: 'audience', title: '人群画像' },
  { id: 'traffic', title: '流量分析' },
  { id: 'qianchuan', title: '千川数据看板' }
]);

// 千川数据
const qianchuanData = ref([
  { label: '总投放预算', value: '¥50,000', trend: '+15.2%', status: 'up' },
  { label: '已消耗预算', value: '¥32,456', trend: '+12.8%', status: 'up' },
  { label: '点击转化率', value: '4.5%', trend: '+0.8%', status: 'up' },
  { label: '展现量', value: '89,234', trend: '+21.5%', status: 'up' },
  { label: '点击量', value: '12,345', trend: '+18.3%', status: 'up' },
  { label: '平均点击成本', value: '¥2.63', trend: '-5.2%', status: 'down' },
  { label: '千次展现成本', value: '¥363.73', trend: '-3.8%', status: 'down' },
  { label: '投放ROI', value: '3.21', trend: '+8.9%', status: 'up' },
  { label: '新增粉丝数', value: '2,345', trend: '+25.6%', status: 'up' }
]);


// 图表引用
const productChartRef = ref<HTMLElement>();
const audienceChartRef = ref<HTMLElement>();
const trafficChartRef = ref<HTMLElement>();
onMounted(() => {
  // 商品销售趋势图
  if (productChartRef.value) {
    const productChart = echarts.init(productChartRef.value);
    productChart.setOption({
      animation: false,
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'cross',
          label: {
            backgroundColor: '#6a7985'
          }
        }
      },
      legend: {
        data: ['商品销量', '销售额', '新品转化率'],
        top: '15'
      },
      grid: {
        left: '3%',
        right: '4%',
        bottom: '3%',
        containLabel: true
      },
      xAxis: [{
        type: 'category',
        boundaryGap: false,
        data: ['1月', '2月', '3月', '4月', '5月', '6月', '7月'],
        axisLine: {
          lineStyle: {
            color: '#999'
          }
        },
        axisLabel: {
          color: '#666'
        }
      }],
      yAxis: [{
        type: 'value',
        name: '销量/销售额',
        position: 'left',
        axisLine: {
          lineStyle: {
            color: '#999'
          }
        },
        axisLabel: {
          formatter: '{value}'
        }
      },
      {
        type: 'value',
        name: '转化率',
        position: 'right',
        axisLine: {
          lineStyle: {
            color: '#999'
          }
        },
        axisLabel: {
          formatter: '{value}%'
        }
      }],
      series: [{
        name: '商品销量',
        type: 'line',
        smooth: true,
        lineStyle: {
          width: 3,
          color: '#5470c6'
        },
        areaStyle: {
          opacity: 0.2,
          color: {
            type: 'linear',
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [{
              offset: 0,
              color: '#5470c6'
            },
            {
              offset: 1,
              color: 'rgba(84,112,198,0.1)'
            }
            ]
          }
        },
        emphasis: {
          focus: 'series'
        },
        data: [150, 230, 224, 218, 235, 247, 260]
      },
      {
        name: '销售额',
        type: 'line',
        smooth: true,
        lineStyle: {
          width: 3,
          color: '#91cc75'
        },
        areaStyle: {
          opacity: 0.2,
          color: {
            type: 'linear',
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [{
              offset: 0,
              color: '#91cc75'
            },
            {
              offset: 1,
              color: 'rgba(145,204,117,0.1)'
            }
            ]
          }
        },
        emphasis: {
          focus: 'series'
        },
        data: [320, 432, 401, 454, 390, 530, 510]
      },
      {
        name: '新品转化率',
        type: 'line',
        yAxisIndex: 1,
        smooth: true,
        lineStyle: {
          width: 3,
          color: '#fac858'
        },
        emphasis: {
          focus: 'series'
        },
        data: [4.5, 5.2, 5.8, 6.2, 5.9, 6.8, 7.1]
      }]
    });
  }
  // 人群画像图
  if (audienceChartRef.value) {
    const audienceChart = echarts.init(audienceChartRef.value);
    audienceChart.setOption({
      animation: false,
      tooltip: {
        trigger: 'item',
        formatter: '{b}: {c}%'
      },
      legend: {
        orient: 'vertical',
        left: 'left',
        top: 'middle'
      },
      series: [{
        type: 'pie',
        radius: ['45%', '75%'],
        center: ['65%', '50%'],
        avoidLabelOverlap: false,
        itemStyle: {
          borderRadius: 10,
          borderColor: '#fff',
          borderWidth: 2
        },
        label: {
          show: false
        },
        emphasis: {
          label: {
            show: true,
            fontSize: '16',
            fontWeight: 'bold'
          }
        },
        labelLine: {
          show: false
        },
        data: [
          { value: 38, name: '18-25岁', itemStyle: { color: '#5470c6' } },
          { value: 32, name: '26-35岁', itemStyle: { color: '#91cc75' } },
          { value: 18, name: '36-45岁', itemStyle: { color: '#fac858' } },
          { value: 12, name: '其他年龄', itemStyle: { color: '#ee6666' } }
        ]
      }, {
        name: '性别分布',
        type: 'pie',
        radius: ['15%', '25%'],
        center: ['65%', '50%'],
        label: {
          show: false
        },
        data: [
          { value: 75, name: '女性', itemStyle: { color: '#ff9a9e' } },
          { value: 25, name: '男性', itemStyle: { color: '#a1c4fd' } }
        ]
      }]
    });
  }
  // 流量分析图
  if (trafficChartRef.value) {
    const trafficChart = echarts.init(trafficChartRef.value);
    trafficChart.setOption({
      animation: false,
      tooltip: {
        trigger: 'axis',
        axisPointer: { type: 'shadow' }
      },
      legend: {
        data: ['访问人数', '转化人数'],
        top: '15'
      },
      grid: {
        left: '3%',
        right: '4%',
        bottom: '3%',
        containLabel: true
      },
      xAxis: {
        type: 'category',
        data: ['首页推荐', '搜索', '短视频', '橱窗', '关注', '分享', '其他'],
        axisLabel: { interval: 0 }
      },
      yAxis: [{
        type: 'value',
        name: '访问人数',
        min: 0,
        max: 1000,
        interval: 200
      }, {
        type: 'value',
        name: '转化率',
        min: 0,
        max: 100,
        interval: 20,
        axisLabel: {
          formatter: '{value}%'
        }
      }],
      series: [{
        name: '访问人数',
        type: 'bar',
        barWidth: '30%',
        data: [850, 620, 580, 450, 420, 380, 240],
        itemStyle: {
          color: {
            type: 'linear',
            x: 0, y: 0, x2: 0, y2: 1,
            colorStops: [{
              offset: 0, color: '#83bff6'
            }, {
              offset: 1, color: '#188df0'
            }]
          }
        }
      }, {
        name: '转化人数',
        type: 'bar',
        barWidth: '30%',
        data: [320, 250, 220, 180, 155, 140, 90],
        itemStyle: {
          color: {
            type: 'linear',
            x: 0, y: 0, x2: 0, y2: 1,
            colorStops: [{
              offset: 0, color: '#ea7ccc'
            }, {
              offset: 1, color: '#c94a87'
            }]
          }
        }
      }]
    });
  }
});
</script>
<style scoped>
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb {
  background: #888;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: #555;
}

input[type="number"]::-webkit-inner-spin-button,
input[type="number"]::-webkit-outer-spin-button {
  -webkit-appearance: none;
  margin: 0;
}

.animate-spin {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }

  to {
    transform: rotate(360deg);
  }
}

/* 删除以下卡片悬浮效果相关的样式 */
/* .bg-white.rounded-lg.shadow-sm {
  transition: all 0.3s ease;
}

.bg-white.rounded-lg.shadow-sm:hover {
  transform: translateY(-4px);
  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
} */
</style>
