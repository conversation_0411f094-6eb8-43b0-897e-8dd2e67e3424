<script setup lang="ts">
import { ref, reactive, onMounted, watch, onBeforeUpdate } from 'vue'
import { UploadFilled, Refresh, Delete, More, VideoPlay, VideoPause } from '@element-plus/icons-vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { useUserStore } from '@/stores/userStore'
import UploadMeetingDialog from '@/components/UploadMeetingDialog.vue'
import MeetingDetailDialog from '@/components/MeetingDetailDialog.vue'
import { 
  meetingApi, 
  type MeetingResponse as BaseMeetingResponse, 
  type PaginatedMeetingResponse,
  type MeetingDetailResponse,
  type CeleryTaskResult,
  type TranscriptionResult,
  type SpeakerSegment
} from '@/api/meetingApi'

// 扩展 MeetingResponse 接口，添加 audioUrl 字段
interface MeetingResponse extends BaseMeetingResponse {
  audioUrl?: string | null
}

interface AudioFile {
  id: string
  name: string
  size: string | number
  duration: number
  status: 'uploaded' | 'processing' | 'done' | 'error'
  fileType?: 'audio' | 'video'
}

interface LineItem {
  speaker: string
  start: number
  end: number
  text: string
}

interface ColumnItem {
  id: string
  name: string
  resultLines: LineItem[]
  loading?: boolean
  activeLineIndex?: number
}

// 添加会议创建请求接口
interface CreateMeetingRequest {
  name: string
  meetType?: string
  users?: string
  desc?: string
  audioOriginUrls: string
  audioOriginSize?: number
  audioDuration?: number
  audioUrl?: string
  audioSize?: number
  audioState: string
}

// 音频播放器状态
const audioPlayer = reactive({
  isPlaying: false,
  currentTime: 0,
  duration: 0,
  currentMeeting: null as MeetingResponse | null,
  audioElement: null as HTMLAudioElement | null,
  audioUrl: ''
})

const userStore = useUserStore()

const loginForm = reactive({
  account: '',
  password: ''
})

const audioFiles = ref<AudioFile[]>([])
const columns = ref<ColumnItem[]>([])
const isLoggingIn = ref(false)
const showUploadDialog = ref(false)

// 会议列表相关状态
const meetingList = ref<MeetingResponse[]>([])
const loading = ref(false)
const pagination = reactive({
  current: 1,
  pageSize: 10,
  total: 0,
  totalPages: 0
})

// 会议详情弹窗相关状态
const showDetailDialog = ref(false)
const selectedMeeting = ref<MeetingResponse | null>(null)

// 用于转写文本自动滚动的 refs
const lineRefs = reactive<Record<string, Array<HTMLElement | null>>>({})
const setLineRef = (colId: string, index: number, el: any) => {
  if (!lineRefs[colId]) {
    lineRefs[colId] = []
  }
  lineRefs[colId][index] = el as HTMLElement | null
}

// 每次更新前清空 refs，防止内存泄漏
onBeforeUpdate(() => {
  for (const key in lineRefs) {
    lineRefs[key] = []
  }
})

// 监听音频播放时间，实现文本高亮和自动滚动
watch(() => audioPlayer.currentTime, (newTime) => {
  if (!audioPlayer.currentMeeting) {
    return
  }

  for (const col of columns.value) {
    const newIndex = col.resultLines.findIndex(line => newTime >= line.start && newTime < line.end)

    if (newIndex !== -1 && newIndex !== col.activeLineIndex) {
      col.activeLineIndex = newIndex
      
      // 滚动到高亮行
      const lineEl = lineRefs[col.id]?.[newIndex]
      if (lineEl) {
        lineEl.scrollIntoView({ behavior: 'smooth', block: 'center', inline: 'nearest' })
      }
    }
  }
})

// 初始化时检查登录状态
onMounted(() => {
  userStore.initFromStorage()
})

// 停止音频
const stopAudio = () => {
  if (audioPlayer.audioElement) {
    audioPlayer.audioElement.pause()
    audioPlayer.audioElement.currentTime = 0
    audioPlayer.audioElement = null
  }
  audioPlayer.isPlaying = false
  audioPlayer.currentTime = 0
  audioPlayer.duration = 0
  audioPlayer.currentMeeting = null
  audioPlayer.audioUrl = ''

  // 停止播放时清除所有高亮
  columns.value.forEach(col => {
    col.activeLineIndex = undefined
  })
}

// 监听登录状态变化，登录后获取会议列表
watch(() => userStore.isLoggedIn, (newVal) => {
  if (newVal) {
    loadMeetingList()
  } else {
    meetingList.value = []
    audioFiles.value = []
    // 停止音频播放
    stopAudio()
  }
}, { immediate: true })

// 获取会议列表
const loadMeetingList = async () => {
  if (!userStore.isLoggedIn) return
  
  loading.value = true
  try {
    const response = await meetingApi.getMeetingList({
      page: pagination.current,
      page_size: pagination.pageSize
    })
    
    meetingList.value = response.meetings
    pagination.total = response.total
    pagination.totalPages = response.total_pages
    
    // 转换会议数据为AudioFile格式以兼容现有显示
    audioFiles.value = response.meetings.map(meeting => ({
      id: meeting.id.toString(),
      name: meeting.name,
      size: '未知',
      duration: 0,
      status: getAudioFileStatus(meeting.audioState),
      fileType: 'audio' as const
    }))
    
  } catch (error: any) {
    ElMessage.error(error.message || '获取会议列表失败')
  } finally {
    loading.value = false
  }
}

// 转换音频状态
const getAudioFileStatus = (audioState?: string | null): 'uploaded' | 'processing' | 'done' | 'error' => {
  switch (audioState) {
    case 'uploaded': return 'uploaded'
    case 'handing': return 'processing'  // 处理中
    case 'finished': return 'done'       // 已完成
    case 'canceled':
    case 'failed': return 'error'        // 取消或失败
    default: return 'uploaded'
  }
}

// 处理分页变化
const handlePageChange = (page: number) => {
  pagination.current = page
  loadMeetingList()
}

// 处理页面大小变化
const handlePageSizeChange = (size: number) => {
  pagination.pageSize = size
  pagination.current = 1
  loadMeetingList()
}

// 刷新列表
const refreshList = () => {
  loadMeetingList()
}

// 查看会议详情
const viewMeetingDetail = (meeting: MeetingResponse) => {
  selectedMeeting.value = meeting
  showDetailDialog.value = true
}

// 从详情弹窗预览会议
const handlePreviewFromDetail = (meeting: MeetingResponse) => {
  // 转换为AudioFile格式进行预览
  const audioFile = {
    id: meeting.id.toString(),
    name: meeting.name,
    size: '未知',
    duration: 0,
    status: getAudioFileStatus(meeting.audioState),
    fileType: 'audio' as const
  }
  preview(audioFile)
}

// 删除会议
const deleteMeeting = async (meeting: MeetingResponse) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除会议"${meeting.name}"吗？此操作不可撤销。`,
      '确认删除',
      {
        confirmButtonText: '确定删除',
        cancelButtonText: '取消',
        type: 'warning',
        confirmButtonClass: 'el-button--danger'
      }
    )
    
    // 调用删除接口
    await meetingApi.deleteMeetingById(meeting.id)
    
    ElMessage.success(`会议"${meeting.name}"删除成功`)
    
    // 刷新会议列表
    loadMeetingList()
    
  } catch (error: any) {
    if (error !== 'cancel') {
      ElMessage.error(error.message || '删除会议失败')
    }
  }
}

// 从详情弹窗删除会议
const handleDeleteFromDetail = async (meeting: MeetingResponse) => {
  try {
    // 调用删除接口
    await meetingApi.deleteMeetingById(meeting.id)
    
    ElMessage.success(`会议"${meeting.name}"删除成功`)
    
    // 刷新会议列表
    loadMeetingList()
    
  } catch (error: any) {
    ElMessage.error(error.message || '删除会议失败')
  }
}

// 从详情弹窗重新转换会议
const handleRetryFromDetail = async (meeting: MeetingResponse) => {
  try {
    // 调用重新转换接口
    await meetingApi.retryTask(meeting.id)
    
    ElMessage.success(`会议"${meeting.name}"已开始重新转换`)
    
    // 刷新会议列表
    loadMeetingList()
    
  } catch (error: any) {
    ElMessage.error(error.message || '重新转换失败')
  }
}

// 播放音频
const playAudio = async (meeting: MeetingResponse) => {
  try {
    // 如果当前正在播放其他音频，先停止
    if (audioPlayer.audioElement && audioPlayer.currentMeeting?.id !== meeting.id) {
      stopAudio()
    }

    // 如果没有音频URL，尝试获取
    if (!meeting.audioUrl) {
      ElMessage.warning('该会议没有可播放的音频文件')
      return
    }

    // 如果是同一个音频，切换播放/暂停状态
    if (audioPlayer.currentMeeting?.id === meeting.id && audioPlayer.audioElement) {
      if (audioPlayer.isPlaying) {
        pauseAudio()
      } else {
        resumeAudio()
      }
      return
    }

    // 创建新的音频元素
    const audio = new Audio(meeting.audioUrl)
    audioPlayer.audioElement = audio
    audioPlayer.currentMeeting = meeting
    audioPlayer.audioUrl = meeting.audioUrl

    // 设置音频事件监听
    audio.addEventListener('loadedmetadata', () => {
      audioPlayer.duration = audio.duration
    })

    audio.addEventListener('timeupdate', () => {
      audioPlayer.currentTime = audio.currentTime
    })

    audio.addEventListener('ended', () => {
      audioPlayer.isPlaying = false
      audioPlayer.currentTime = 0
    })

    audio.addEventListener('error', (e) => {
      console.error('音频播放错误:', e)
      ElMessage.error('音频播放失败')
      stopAudio()
    })

    // 开始播放
    await audio.play()
    audioPlayer.isPlaying = true
    
    ElMessage.success(`开始播放：${meeting.name}`)
    
  } catch (error: any) {
    console.error('播放音频失败:', error)
    ElMessage.error('播放音频失败')
  }
}

// 暂停音频
const pauseAudio = () => {
  if (audioPlayer.audioElement) {
    audioPlayer.audioElement.pause()
    audioPlayer.isPlaying = false
  }
}

// 恢复播放
const resumeAudio = async () => {
  if (audioPlayer.audioElement) {
    try {
      await audioPlayer.audioElement.play()
      audioPlayer.isPlaying = true
    } catch (error) {
      console.error('恢复播放失败:', error)
      ElMessage.error('恢复播放失败')
    }
  }
}

// 跳转到指定时间
const seekAudio = (time: number) => {
  if (audioPlayer.audioElement) {
    audioPlayer.audioElement.currentTime = time
    audioPlayer.currentTime = time
  }
}

// 处理进度条点击
const handleProgressClick = (event: MouseEvent) => {
  if (!audioPlayer.audioElement || audioPlayer.duration === 0) return
  
  const progressBar = event.currentTarget as HTMLElement
  const rect = progressBar.getBoundingClientRect()
  const clickPosition = event.clientX - rect.left
  const percentage = clickPosition / rect.width
  const newTime = percentage * audioPlayer.duration
  
  seekAudio(newTime)
}

// 格式化音频播放时间显示
const formatAudioTime = (seconds: number) => {
  const minutes = Math.floor(seconds / 60)
  const remainingSeconds = Math.floor(seconds % 60)
  return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`
}

// 获取进度百分比
const getProgressPercentage = () => {
  if (audioPlayer.duration === 0) return 0
  return (audioPlayer.currentTime / audioPlayer.duration) * 100
}

// 处理下拉菜单命令
const handleDropdownCommand = (command: string, meeting: MeetingResponse | undefined) => {
  if (!meeting) {
    ElMessage.error('找不到对应的会议信息')
    return
  }
  switch (command) {
    case 'detail':
      viewMeetingDetail(meeting)
      break
    case 'play':
      playAudio(meeting)
      break
    case 'retry':
      retryTask(meeting)
      break
    case 'delete':
      deleteMeeting(meeting)
      break
  }
}

// 重新转换任务
const retryTask = async (meeting: MeetingResponse) => {
  try {
    await ElMessageBox.confirm(
      `确定要重新转换会议"${meeting.name}"吗？这将重新开始音频处理流程。`,
      '确认重新转换',
      {
        confirmButtonText: '确定转换',
        cancelButtonText: '取消',
        type: 'info'
      }
    )
    
    // 调用重新转换接口
    await meetingApi.retryTask(meeting.id)
    
    ElMessage.success(`会议"${meeting.name}"已开始重新转换`)
    
    // 刷新会议列表
    loadMeetingList()
    
  } catch (error: any) {
    if (error !== 'cancel') {
      ElMessage.error(error.message || '重新转换失败')
    }
  }
}

// 检查是否可以重新转换
const canRetry = (meeting?: MeetingResponse | null) => {
  if (!meeting) return false
  return meeting.audioState !== 'handing' // 非处理中状态可以重试
}

// 处理上传成功
const handleUploadSuccess = (meetingData: CreateMeetingRequest) => {
  console.log('会议数据上传成功:', meetingData)
  
  const fileTypeText = '音频'
  ElMessage.success(`成功上传会议"${meetingData.name}"`)
  
  // 刷新会议列表
  loadMeetingList()
}

const preview = async (row: AudioFile) => {
  // 已存在则不再添加
  if (columns.value.find((c) => c.id === row.id)) return

  // 添加加载状态的列
  const newColumn: ColumnItem = {
    id: row.id,
    name: row.name,
    resultLines: [],
    loading: true
  }
  columns.value.push(newColumn)

  try {
    // 获取会议详情
    const meetingDetail = await meetingApi.getMeetingDetail(parseInt(row.id))
    
    // 解析celeryTaskResult
    const taskResult = meetingApi.parseCeleryTaskResult(meetingDetail.celeryTaskResult)
    
    if (!taskResult) {
      throw new Error('无法获取转录任务结果')
    }

    if (!taskResult.result_url) {
      throw new Error('转录结果URL不存在')
    }
    
    // 获取转录结果
    const transcriptionResult = await meetingApi.getTranscriptionResult(taskResult.result_url)
    
    // 转换为LineItem格式
    const lines: LineItem[] = transcriptionResult.speaker_segments.map((segment: SpeakerSegment) => ({
      speaker: `说话人${segment.speaker}`,
      start: Math.round(segment.start_time * 100) / 100, // 保留2位小数
      end: Math.round(segment.end_time * 100) / 100,
      text: segment.text
    }))

    // 更新列数据
    const columnIndex = columns.value.findIndex((c) => c.id === row.id)
    if (columnIndex !== -1) {
      columns.value[columnIndex].resultLines = lines
      columns.value[columnIndex].loading = false
    }

  } catch (error: any) {
    console.error('获取转录结果失败:', error)
    ElMessage.error(error.message || '获取转录结果失败')
    
    // 移除失败的列
    removeColumn(row.id)
  }
}

const removeColumn = (id: string) => {
  columns.value = columns.value.filter((c) => c.id !== id)
  if (lineRefs[id]) {
    delete lineRefs[id]
  }
}

const login = async () => {
  if (!loginForm.account || !loginForm.password) {
    ElMessage.error('请输入账号和密码')
    return
  }

  isLoggingIn.value = true
  try {
    await userStore.login({
      account: loginForm.account,
      password: loginForm.password
    })
    ElMessage.success('登录成功')
    // 清空表单
    loginForm.account = ''
    loginForm.password = ''
  } catch (error: any) {
    ElMessage.error(error.message || '登录失败')
  } finally {
    isLoggingIn.value = false
  }
}

const logout = () => {
  userStore.logout()
  ElMessage.success('已退出登录')
}

// 获取状态类型
const getStatusType = (status: string) => {
  switch (status) {
    case 'uploaded': return 'success'
    case 'processing': return 'warning'
    case 'done': return 'primary'
    case 'error': return 'danger'
    default: return 'info'
  }
}

// 获取状态文本
const getStatusText = (status: string) => {
  switch (status) {
    case 'uploaded': return '已上传'
    case 'processing': return '处理中'
    case 'done': return '已完成'
    case 'error': return '转换失败'
    default: return '未知'
  }
}

// 获取音频状态显示文本
const getAudioStateText = (audioState?: string | null) => {
  return getStatusText(getAudioFileStatus(audioState))
}

// 获取音频状态显示类型
const getAudioStateType = (audioState?: string | null) => {
  return getStatusType(getAudioFileStatus(audioState))
}

// 格式化创建时间
const formatTime = (timeStr?: string | null) => {
  if (!timeStr) return '-'
  return new Date(timeStr).toLocaleString('zh-CN')
}

// 根据ID获取会议信息
const getMeetingById = (id: string) => {
  return meetingList.value.find(meeting => meeting.id.toString() === id)
}

// 格式化文件大小（字节转MB）
const formatFileSize = (bytes?: number | null) => {
  if (!bytes || bytes === 0) return '-'
  const mb = bytes / (1024 * 1024)
  return `${mb.toFixed(2)} MB`
}

// 格式化时长（秒转分:秒）
const formatDuration = (seconds?: number | null) => {
  if (!seconds || seconds === 0) return '-'
  const minutes = Math.floor(seconds / 60)
  const remainingSeconds = seconds % 60
  return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`
}

// 点击文本行跳转到对应音频时间
const seekToLine = async (line: LineItem, column: ColumnItem) => {
  const meetingToPlay = getMeetingById(column.id)
  if (!meetingToPlay) return

  // 如果当前没有播放，或者播放的不是这个会议，则开始播放这个会议的音频
  if (!audioPlayer.currentMeeting || audioPlayer.currentMeeting.id.toString() !== column.id) {
    await playAudio(meetingToPlay)
  }
  
  // 跳转到指定时间
  seekAudio(line.start)
}
</script>

<template>
  <div class="flex flex-col h-screen">
    <!-- 顶部登录栏 -->
    <div class="p-4 border-b flex items-center space-x-4 flex-shrink-0">
      <template v-if="!userStore.isLoggedIn">
        <el-input v-model="loginForm.account" placeholder="账号" size="small" style="width: 140px" />
        <el-input
          v-model="loginForm.password"
          placeholder="密码"
          size="small"
          show-password
          style="width: 140px"
          @keyup.enter="login"
        />
        <el-button type="primary" size="small" :loading="isLoggingIn" @click="login">
          登录
        </el-button>
      </template>
      <template v-else>
        <span class="text-gray-700">
          欢迎，{{ userStore.userInfo?.nickname || userStore.userInfo?.name || userStore.userInfo?.account }}
        </span>
        <el-button size="small" @click="logout">退出登录</el-button>
      </template>
    </div>

    <!-- 主体区域 -->
    <div class="flex flex-1 min-h-0">
      <!-- 左侧 30% -->
      <div class="w-[35%] p-4 border-r flex flex-col min-h-0">
        <!-- 上传按钮 -->
        <div class="mb-4 flex-shrink-0">
          <el-button
            type="primary"
            size="large"
            :icon="UploadFilled"
            @click="showUploadDialog = true"
            class="w-full h-20"
          >
            <div class="flex flex-col items-center">
              <span class="text-lg font-medium">上传会议音视频</span>
            </div>
          </el-button>
        </div>

        <!-- 音频播放控制器 -->
        <div v-if="audioPlayer.currentMeeting" class="mb-4 p-3 border rounded-lg bg-gray-50">
          <div class="flex items-center justify-between mb-2">
            <span class="text-sm font-medium truncate mr-2">
              {{ audioPlayer.currentMeeting.name }}
            </span>
            <div class="flex items-center space-x-2">
              <el-button
                :icon="audioPlayer.isPlaying ? VideoPause : VideoPlay"
                size="small"
                circle
                @click="audioPlayer.isPlaying ? pauseAudio() : resumeAudio()"
              />
              <el-button
                icon="Close"
                size="small"
                circle
                @click="stopAudio"
                title="停止播放"
              />
            </div>
          </div>
          <!-- 进度条 -->
          <div class="mb-2">
            <div 
              class="w-full h-2 bg-gray-200 rounded-full cursor-pointer"
              @click="handleProgressClick"
            >
              <div 
                class="h-2 bg-blue-500 rounded-full transition-all duration-150"
                :style="{ width: `${getProgressPercentage()}%` }"
              />
            </div>
          </div>
          <!-- 时间显示 -->
          <div class="flex justify-between text-xs text-gray-500">
            <span>{{ formatAudioTime(audioPlayer.currentTime) }}</span>
            <span>{{ formatAudioTime(audioPlayer.duration) }}</span>
          </div>
        </div>

        <!-- 列表 -->
        <div class="flex-1 min-h-0 flex flex-col">
          <div class="text-sm text-gray-600 mb-2 flex-shrink-0 flex justify-between items-center">
            <span>会议音视频列表 ({{ pagination.total }})</span>
            <el-button 
              :icon="Refresh" 
              size="small" 
              :loading="loading"
              @click="refreshList"
              circle
              title="刷新列表"
            />
          </div>
          <div class="flex-1 min-h-0 flex flex-col">
            <div class="flex-1 min-h-0">
              <el-table 
                :data="audioFiles" 
                size="small" 
                :max-height="'100%'"
                row-key="id" 
                style="height: 100%;"
                v-loading="loading"
              >
                <el-table-column prop="name" label="会议名称" show-overflow-tooltip min-width="120" />
                <el-table-column label="文件大小" width="100" align="right">
                  <template #default="{ row }">
                    {{ formatFileSize(getMeetingById(row.id)?.audioOriginSize) }}
                  </template>
                </el-table-column>
                <el-table-column label="时长" width="80" align="right">
                  <template #default="{ row }">
                    {{ formatDuration(getMeetingById(row.id)?.audioDuration) }}
                  </template>
                </el-table-column>
                <el-table-column label="创建时间" width="140">
                  <template #default="{ row }">
                    {{ formatTime(getMeetingById(row.id)?.created_at) }}
                  </template>
                </el-table-column>
                <el-table-column label="状态" width="100">
                  <template #default="{ row }">
                    <el-tag 
                      :type="getStatusType(row.status)" 
                      size="small"
                    >
                      {{ getStatusText(row.status) }}
                    </el-tag>
                  </template>
                </el-table-column>
                <el-table-column fixed="right" label="操作" width="120">
                  <template #default="{ row }">
                    <div class="flex gap-1 items-center">
                      <el-button 
                        link 
                        size="small" 
                        @click="preview(row)"
                        :disabled="row.status !== 'done'"
                      >
                        预览
                      </el-button>
                      
                      <el-dropdown trigger="click" @command="(command: string) => handleDropdownCommand(command, getMeetingById(row.id))">
                        <el-button 
                          link 
                          size="small"
                          :icon="More"
                          circle
                        />
                        <template #dropdown>
                          <el-dropdown-menu>
                            <el-dropdown-item command="detail">查看详情</el-dropdown-item>
                            <el-dropdown-item 
                              command="play"
                              v-if="getMeetingById(row.id)?.audioUrl"
                            >
                              播放音频
                            </el-dropdown-item>
                            <el-dropdown-item 
                              command="retry" 
                              v-if="canRetry(getMeetingById(row.id))"
                            >
                              重新转换
                            </el-dropdown-item>
                            <el-dropdown-item command="delete" divided>
                              <span style="color: #f56c6c;">删除会议</span>
                            </el-dropdown-item>
                          </el-dropdown-menu>
                        </template>
                      </el-dropdown>
                    </div>
                  </template>
                </el-table-column>
              </el-table>
            </div>
            <!-- 分页 -->
            <div class="flex-shrink-0 pt-4" v-if="pagination.total > 0">
              <el-pagination
                v-model:current-page="pagination.current"
                v-model:page-size="pagination.pageSize"
                :page-sizes="[10, 20, 50, 100]"
                :small="true"
                layout="total, sizes, prev, pager, next"
                :total="pagination.total"
                @size-change="handlePageSizeChange"
                @current-change="handlePageChange"
              />
            </div>
          </div>
        </div>
      </div>

      <!-- 右侧 70% 预览区域 -->
      <div class="flex-1 p-4 min-h-0 overflow-x-auto">
        <div class="flex space-x-4 h-full">
          <div
            v-for="col in columns"
            :key="col.id"
            class="border rounded p-2 w-72 flex-shrink-0 flex flex-col h-full"
          >
            <div class="flex justify-between items-center mb-2 flex-shrink-0">
              <span class="font-medium">{{ col.name }}</span>
              <el-button link size="small" @click="removeColumn(col.id)">关闭</el-button>
            </div>
            
            <div class="flex-1 min-h-0 overflow-y-auto pr-2">
              <!-- 加载状态 -->
              <div v-if="col.loading" class="flex items-center justify-center py-8">
                <el-icon class="is-loading mr-2"><Refresh /></el-icon>
                <span class="text-gray-500">正在获取转录结果...</span>
              </div>
              
              <!-- 转录结果 -->
              <div v-else>
                <div v-if="col.resultLines.length === 0" class="text-center py-8 text-gray-500">
                  暂无转录结果
                </div>
                <div v-else>
                  <div 
                    v-for="(line, index) in col.resultLines" 
                    :key="index" 
                    class="mb-2 p-1 rounded cursor-pointer transition-colors"
                    :ref="el => setLineRef(col.id, index, el)"
                    :class="{ 'bg-blue-100': index === col.activeLineIndex }"
                    @click="seekToLine(line, col)"
                  >
                    <div class="text-xs text-gray-600">
                      <span class="font-semibold mr-1">{{ line.speaker }}</span>
                      {{ line.start }}s - {{ line.end }}s
                    </div>
                    <div class="text-sm">{{ line.text }}</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 上传弹窗 -->
    <UploadMeetingDialog
      v-model="showUploadDialog"
      @success="handleUploadSuccess"
    />

    <!-- 会议详情弹窗 -->
    <MeetingDetailDialog
      v-model="showDetailDialog"
      :meeting="selectedMeeting"
      @preview="handlePreviewFromDetail"
      @delete="handleDeleteFromDetail"
      @retry="handleRetryFromDetail"
    />
  </div>
</template>
