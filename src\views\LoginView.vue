<!-- 代码已包含 CSS：使用 TailwindCSS , 安装 TailwindCSS 后方可看到布局样式效果 -->
<template>
  <div class="min-h-screen bg-gray-50 flex flex-col items-center justify-center py-12 px-4 sm:px-6 lg:px-8">
    <div class="flex items-center justify-center mb-8 space-x-4">
      <img class="h-10 sm:h-12 w-auto" src="@/assets/logo.jpg"
        alt="Logo" />
      <h2 class="text-lg sm:text-xl font-bold text-gray-900">定制美好生活</h2>
    </div>
    <div class="min-h-[420px] bg-white rounded-2xl shadow-lg p-[12px] flex flex-col sm:flex-row w-full max-w-[700px]">
      <!-- 左侧登录区域 -->
      <div class="left-panel w-full sm:w-auto flex items-center justify-center">
        <div class="space-y-6 sm:space-y-8 w-full max-w-[320px]">
          <!-- 登录方式切换 -->
          <div class="flex justify-center space-x-4 border-b border-gray-200 pb-2">
            <button
              v-for="tab in tabs"
              :key="tab.key"
              @click="currentTab = tab.key"
              :class="[
                'px-4 py-2 text-sm font-medium',
                currentTab === tab.key
                  ? 'text-indigo-600 border-b-2 border-indigo-600'
                  : 'text-gray-500 hover:text-gray-700'
              ]"
            >
              {{ tab.name }}
            </button>
          </div>

          <!-- 测试账号提示 -->
          <!-- <div class="bg-blue-50 p-3 rounded-lg text-sm text-blue-800 border border-blue-200">
            <div class="font-medium mb-1">测试账号：</div>
            <div v-if="currentTab === 'password'">
              <div>账号：<span class="font-mono">admin</span></div>
              <div>密码：<span class="font-mono">123456</span></div>
            </div>
            <div v-else>
              <div>手机号：<span class="font-mono">***********</span></div>
              <div>验证码：<span class="font-mono">任意数字</span></div>
            </div>
          </div> -->

          <!-- 手机号/账号输入 -->
          <div class="relative">
            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <el-icon class="text-gray-400">
                <Phone />
              </el-icon>
            </div>
            <input type="text"
              class="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-lg text-sm placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-transparent"
              :placeholder="currentTab === 'phone' ? '请输入手机号' : '请输入账号'" v-model="phone" />
          </div>

          <!-- 验证码/密码输入 -->
          <div class="relative mt-6">
            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <template v-if="currentTab === 'phone'">
                <span class="text-gray-400 font-bold">#</span>
              </template>
              <template v-else>
                <el-icon class="text-gray-400">
                  <Lock />
                </el-icon>
              </template>
            </div>
            <input :type="currentTab === 'phone' ? 'text' : 'password'"
              class="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-lg text-sm placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-transparent"
              :placeholder="currentTab === 'phone' ? '请输入验证码' : '请输入密码'" v-model="password" />
            <button v-if="currentTab === 'phone'"
              class="absolute inset-y-0 right-0 px-3 flex items-center text-sm font-medium text-indigo-600 hover:text-indigo-500 disabled:text-gray-400"
              @click="sendCode" :disabled="counting">
              {{ codeText }}
            </button>
          </div>

          <!-- 登录按钮 -->
          <button
            class="w-full py-2 px-4 border border-transparent rounded-lg shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
            @click="handleLogin"
          >
            登录
          </button>

          <!-- 其他选项 -->
          <div class="flex justify-between text-sm">
            <router-link to="/forgot-password" class="text-indigo-600 hover:text-indigo-500">
              忘记密码?
            </router-link>
            <button @click="handleRegister" class="text-indigo-600 hover:text-indigo-500 bg-transparent border-none cursor-pointer">
              注册账号
            </button>
          </div>
        </div>
      </div>
      <!-- 右侧二维码区域 - 在移动端隐藏 -->
      <div class="hidden sm:flex w-[280px] flex-col items-center justify-center">
        <div class="w-full h-full bg-gray-100 rounded-lg flex flex-col items-center justify-center p-4">
          <QRCode 
            :value="qrcodeValue" 
            :expiry-time="10" 
            :size="160"
          />
          <div class="flex items-center mt-4 space-x-2 text-gray-600">
            <el-icon>
              <ChatRound />
            </el-icon>
            <span>微信扫码登录</span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script lang="ts" setup>
import { ref, onBeforeUnmount } from 'vue';
import { useRouter } from 'vue-router';
import { Phone, Lock, ChatRound } from '@element-plus/icons-vue';
import QRCode from '@/components/QRCode.vue';
import { useUserStore } from '@/stores/userStore';
import { ElMessage } from 'element-plus';

const router = useRouter();
const tabs = [
  { key: 'phone', name: '验证码登录' },
  { key: 'password', name: '密码登录' }
];
const currentTab = ref('phone');
const phone = ref('');
const password = ref('');
const codeText = ref('发送验证码');
const counting = ref(false);
let timer: number;

// 二维码内容，实际应用中应该从后端获取包含认证信息的URL
const qrcodeValue = ref('https://fuyangshuzhi.com/login?qr=wx' + Date.now());

const startCountDown = () => {
  let count = 60;
  counting.value = true;
  timer = window.setInterval(() => {
    if (count > 0) {
      codeText.value = `${count}秒后重新获取`;
      count--;
    } else {
      clearInterval(timer);
      codeText.value = '发送验证码';
      counting.value = false;
    }
  }, 1000);
};

const sendCode = () => {
  if (counting.value) return;
  if (!phone.value) {
    alert('请输入手机号');
    return;
  }
  // TODO: 实现发送验证码逻辑
  startCountDown();
};

const userStore = useUserStore();

const handleLogin = async () => {
  if (!phone.value) {
    ElMessage.warning('请输入手机号');
    return;
  }
  if (!password.value) {
    ElMessage.warning(currentTab.value === 'phone' ? '请输入验证码' : '请输入密码');
    return;
  }
  
  // Mock登录逻辑
  try {
    // 模拟账号密码: admin/123456 或手机号***********验证码任意
    if ((currentTab.value === 'password' && phone.value === 'admin' && password.value === '123456') || 
        (currentTab.value === 'phone' && phone.value === '***********')) {
      
      // 设置用户信息
      await userStore.login({
        account: phone.value,
        password: password.value
      });
      
      // 不需要在这里设置userInfo，因为已经在userStore的login方法中设置了
      
      ElMessage.success('登录成功');
      router.push('/profile');
    } else {
      ElMessage.error('账号或密码错误');
    }
  } catch (error) {
    ElMessage.error('登录失败，请稍后重试');
    console.error(error);
  }
};

const handleForgotPassword = () => {
  // 跳转到忘记密码页面
  router.push('/forgot-password');
};

const handleRegister = () => {
  // 提示系统暂时不开放注册
  ElMessage.info('系统暂时不开放注册，敬请期待');
};

// 组件卸载时清除定时器
onBeforeUnmount(() => {
  if (timer) {
    clearInterval(timer);
  }
});
</script>
<style scoped>
.bg-gray-50 {
  background: linear-gradient(135deg, #f6f7ff 0%, #f0f3ff 100%);
  min-height: 100vh;
}
.left-panel {
    box-sizing: border-box;
    flex-direction: column;
    flex-grow: 1;
    /* width: min(408px, 100vw - 24px); */
    min-width: 300px;
    padding: 32px 28px 24px;
    display: flex;
}

/* 移动端适配样式 */
@media (max-width: 640px) {
  .left-panel {
    padding: 24px 20px 16px;
    min-width: auto;
  }
  
  input {
    font-size: 14px;
    padding-top: 10px;
    padding-bottom: 10px;
  }
  
  button {
    font-size: 14px;
  }
  
  /* 移动端居中和宽度限制 */
  .left-panel > div {
    width: 100%;
    max-width: 280px;
    margin: 0 auto;
  }
}
</style>
