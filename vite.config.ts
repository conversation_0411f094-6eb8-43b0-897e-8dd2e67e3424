import { fileURLToPath, URL } from 'node:url'

import { defineConfig , loadEnv} from 'vite'
import vue from '@vitejs/plugin-vue'
import vueJsx from '@vitejs/plugin-vue-jsx'
import vueDevTools from 'vite-plugin-vue-devtools'
import tailwindcss from '@tailwindcss/vite'
import qiankun from "vite-plugin-qiankun";
import cssInjectedByJsPlugin from 'vite-plugin-css-injected-by-js'

const useDevMode = true
// https://vite.dev/config/
export default defineConfig(({command, mode})=>{
   // 读取 .env.[mode] 中的变量，方便按环境切换
   const { VITE_PUBLIC_PATH } = loadEnv(mode, process.cwd())


  return {
    base: command === 'build'
    ? (VITE_PUBLIC_PATH || 'https://your.cdn.com/dataAnalysis/')
    : './',
    
  plugins: [
    tailwindcss(),
    vue(),
    vueJsx(),
    vueDevTools(),
    cssInjectedByJsPlugin(),
    qiankun('dataAnalysis', {
      useDevMode
    })
  ],
  resolve: {
    alias: {
      '@': fileURLToPath(new URL('./src', import.meta.url))
    },
  },
  css: {
    postcss: {
      plugins: [
        {
          postcssPlugin: 'namespace-css',
          Once(root) {
            if (root.source && root.source.input.file && root.source.input.file.includes('element-plus')) {
              return;
            }
            
            root.walkRules(rule => {
              if (rule.selector.includes(':root') || 
                  rule.selector.includes('html') || 
                  rule.selector.includes('body') ||
                  rule.selector.startsWith('@') ||
                  rule.selector.includes('el-')) {
                return;
              }
              
              rule.selector = rule.selector
                .split(',')
                .map(s => `html[data-qiankun-app="dataAnalysis"] ${s.trim()}`)
                .join(',');
            });
          }
        }
      ]
    }
  }
}
})
